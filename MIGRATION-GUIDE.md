# Complete Clerk to Auth0 Migration Guide

This guide covers the complete migration from Clerk authentication to Auth0, including both frontend changes and user data migration.

## Migration Overview

The migration consists of two main parts:
1. **Frontend Migration**: Replace Clerk with Auth0 in the React application ✅ **COMPLETED**
2. **User Data Migration**: Migrate all users from Supabase Auth to Auth0

## Part 1: Frontend Migration ✅ COMPLETED

The frontend has been successfully migrated from Clerk to Auth0:

### What Was Changed:
- ✅ Removed all Clerk dependencies and code
- ✅ Installed and configured Auth0 React SDK
- ✅ Created new Auth0 authentication components
- ✅ Updated all login/logout flows
- ✅ Modified auth state management to work with Auth0
- ✅ Updated all user references throughout the application
- ✅ Preserved existing UI/UX and styling

### Current Status:
- The application builds successfully
- All authentication flows use Auth0
- User state is properly managed
- Protected routes work with Auth0

### Required Configuration:
You need to set up your Auth0 application and update these environment variables in `frontend/.env`:
```
VITE_AUTH0_DOMAIN=your-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_AUDIENCE=your-auth0-api-identifier
```

## Part 2: User Data Migration

### Overview
The user migration scripts will:
1. Export all users from Supabase Auth
2. Create corresponding users in Auth0
3. Update all database references to use Auth0 user IDs
4. Preserve all user relationships and data

### Migration Scripts Location
All migration scripts are in the `migration/` directory:

```
migration/
├── README.md                     # Detailed migration instructions
├── package.json                  # Dependencies and scripts
├── setup.sh                      # Setup script
├── .env.example                  # Environment template
├── supabase-to-auth0-migration.js # Main migration script
├── dry-run-migration.js          # Test migration without changes
├── rollback-migration.js         # Rollback database changes
└── post-migration-tasks.js       # Post-migration tasks
```

### Quick Start

1. **Setup the migration environment:**
   ```bash
   cd migration
   ./setup.sh
   ```

2. **Configure your credentials:**
   Edit `migration/.env` with your actual Auth0 and Supabase credentials.

3. **Run a dry run to see what will happen:**
   ```bash
   npm run dry-run
   ```

4. **If the dry run looks good, run the migration:**
   ```bash
   npm run migrate
   ```

5. **Run post-migration tasks:**
   ```bash
   npm run post-migration
   ```

### What Gets Migrated

**User Data:**
- Email addresses
- Names and profile information
- Email verification status
- User metadata
- Creation dates

**Database Relationships:**
- User profiles (`profiles` table)
- User-tenant mappings (`user_tenants` table)
- User roles (`user_roles` table)
- User group memberships (`user_group_memberships` table)
- Inferred roles (`inferred_roles` table)

**What Doesn't Get Migrated:**
- Passwords (users will need to reset them)
- Session data
- Supabase-specific authentication tokens

### Post-Migration Tasks

After migration, the scripts will:
1. Send password reset emails to all users
2. Update user metadata with migration information
3. Generate communication templates
4. Create a comprehensive migration report

## Part 3: Backend Integration (Manual)

After user migration, you'll need to update your Supabase edge functions to validate Auth0 JWTs instead of Supabase tokens.

### Required Changes:
1. **Update JWT validation** in edge functions
2. **Modify user identification** logic
3. **Test API calls** with Auth0 tokens

### Edge Functions to Update:
- `get-entra-token`
- `fetch-entra-data`
- `process-tenant-data`
- `infer-roles`
- `manage-webhooks`

## Migration Timeline

### Recommended Schedule:

**Phase 1: Preparation (1-2 days)**
- Set up Auth0 tenant and application
- Configure environment variables
- Run dry-run migration
- Plan user communication

**Phase 2: Migration (1 day)**
- Run the user migration during low-traffic hours
- Verify migration results
- Send password reset emails to users

**Phase 3: Backend Updates (2-3 days)**
- Update edge functions for Auth0 JWT validation
- Test all API endpoints
- Monitor for authentication issues

**Phase 4: Cleanup (1 week later)**
- Verify all users have successfully logged in
- Clean up old Supabase auth data (optional)
- Remove migration scripts (optional)

## Important Considerations

### User Communication
- Users will need to reset their passwords
- Send clear instructions about the migration
- Provide support for users having trouble

### Security
- Auth0 user IDs are different from Supabase user IDs
- All database relationships are automatically updated
- Original Supabase IDs are preserved in Auth0 metadata

### Rollback Plan
- Database changes can be rolled back using the rollback script
- Auth0 users would need to be manually cleaned up
- Keep migration mapping files for reference

## Support and Troubleshooting

### Common Issues:
1. **Rate limiting**: Auth0 has limits on user creation
2. **Duplicate emails**: Existing Auth0 users won't be recreated
3. **Permission errors**: Ensure correct Auth0 scopes

### Getting Help:
1. Check the migration logs and error messages
2. Review the generated mapping files
3. Run verification scripts to check data integrity
4. Consult the detailed README in the migration directory

## Success Criteria

The migration is successful when:
- ✅ All users can log in with Auth0
- ✅ All user data and relationships are preserved
- ✅ The application functions normally with Auth0
- ✅ Edge functions validate Auth0 tokens correctly
- ✅ No authentication-related errors in production

## Next Steps

1. **Set up Auth0**: Create your Auth0 tenant and application
2. **Configure environment variables**: Update both frontend and migration configs
3. **Test the frontend**: Ensure Auth0 login works in development
4. **Run migration**: Follow the migration scripts in the `migration/` directory
5. **Update backend**: Modify edge functions for Auth0 JWT validation
6. **Monitor and support**: Help users through the transition

The frontend migration is complete and ready for Auth0. The user data migration scripts are prepared and ready to run when you're ready to migrate your users.
