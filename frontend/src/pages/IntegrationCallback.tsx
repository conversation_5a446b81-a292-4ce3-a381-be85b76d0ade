import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Integration Callback Page
 * 
 * This page handles MSAL redirects for Entra ID integration authentication.
 * It's completely separate from Auth0 authentication to avoid conflicts.
 * 
 * The page will:
 * 1. Handle the MSAL redirect response
 * 2. Extract any authentication results
 * 3. Redirect back to the integration setup page
 * 4. Display appropriate loading/error states
 */
const IntegrationCallback: React.FC = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('Processing integration authentication...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Check if this is an MSAL callback by looking for specific URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const hasCode = urlParams.has('code');
        const hasState = urlParams.has('state');
        const hasError = urlParams.has('error');

        if (hasError) {
          const error = urlParams.get('error');
          const errorDescription = urlParams.get('error_description');
          console.error('[Integration Callback] Authentication error:', error, errorDescription);
          
          setStatus('error');
          setMessage(`Authentication failed: ${errorDescription || error || 'Unknown error'}`);
          
          // Redirect back to integration setup after a delay
          setTimeout(() => {
            navigate('/integration-setup', { 
              state: { 
                error: `Authentication failed: ${errorDescription || error}` 
              } 
            });
          }, 3000);
          return;
        }

        if (hasCode && hasState) {
          console.log('[Integration Callback] Processing MSAL authentication response');
          setMessage('Authentication successful! Redirecting...');
          setStatus('success');
          
          // Let MSAL handle the response in the background
          // The actual token processing will be handled by the MSAL instance
          // when the integration setup page calls getAccessToken
          
          // Redirect back to integration setup after a short delay
          setTimeout(() => {
            navigate('/integration-setup', { 
              state: { 
                authSuccess: true,
                message: 'Integration authentication completed successfully' 
              } 
            });
          }, 2000);
        } else {
          // No relevant parameters found, might be a direct navigation
          console.log('[Integration Callback] No authentication parameters found, redirecting to integration setup');
          setMessage('Redirecting to integration setup...');
          setStatus('success');
          
          setTimeout(() => {
            navigate('/integration-setup');
          }, 1000);
        }
      } catch (error: any) {
        console.error('[Integration Callback] Error processing callback:', error);
        setStatus('error');
        setMessage(`Error processing authentication: ${error.message || 'Unknown error'}`);
        
        // Redirect back to integration setup after a delay
        setTimeout(() => {
          navigate('/integration-setup', { 
            state: { 
              error: `Error processing authentication: ${error.message}` 
            } 
          });
        }, 3000);
      }
    };

    handleCallback();
  }, [navigate]);

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return '#2196F3';
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#F44336';
      default:
        return '#666';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return '⏳';
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⏳';
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      backgroundColor: '#f5f5f5',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      padding: '20px',
      textAlign: 'center'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '40px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        maxWidth: '500px',
        width: '100%'
      }}>
        <div style={{
          fontSize: '48px',
          marginBottom: '20px'
        }}>
          {getStatusIcon()}
        </div>
        
        <h1 style={{
          color: getStatusColor(),
          fontSize: '24px',
          marginBottom: '16px',
          fontWeight: '600'
        }}>
          Integration Authentication
        </h1>
        
        <p style={{
          color: '#666',
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '20px'
        }}>
          {message}
        </p>
        
        {status === 'loading' && (
          <div style={{
            display: 'inline-block',
            width: '20px',
            height: '20px',
            border: '2px solid #f3f3f3',
            borderTop: '2px solid #2196F3',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
        )}
        
        {status === 'error' && (
          <button
            onClick={() => navigate('/integration-setup')}
            style={{
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '12px 24px',
              fontSize: '14px',
              cursor: 'pointer',
              fontWeight: '500'
            }}
          >
            Return to Integration Setup
          </button>
        )}
      </div>
      
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default IntegrationCallback;
