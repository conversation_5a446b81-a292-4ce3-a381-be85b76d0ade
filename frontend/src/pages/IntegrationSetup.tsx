// Explicitly import React to ensure it's available
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { useRolewiseStore } from '../stores/rolewiseStore';
import { useAuthStore } from '../stores/authStore';
import { EntraIdIntegrationProvider, useEntraIdIntegration, IntegrationConfig } from '../contexts/EntraIdIntegrationContext';
import IntegrationWizard from '../components/IntegrationWizard/IntegrationWizard';
import IntegrationSetupGuide from '../components/IntegrationSetupGuide/IntegrationSetupGuide';
import { validateIntegrationConfig, getErrorGuidance } from '../services/integrationValidation';
import { supabaseService } from '../services/api/supabaseService';
import * as styles from './integrationSetupStyles.css.ts';

// Main component content that uses the integration context
const IntegrationSetupContent: React.FC = () => {
  // Add error handling for style imports
  if (!styles || !styles.container) {
    console.error('Styles not loaded properly:', styles);
  }

  const location = useLocation();
  const { state: integrationState, actions: integrationActions } = useEntraIdIntegration();
  const { loading: contextLoading, error: contextError } = useRolewiseStore();
  const { user } = useAuthStore();

  const [tenantId, setTenantId] = useState(''); // Entra ID tenant GUID
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [appTenantId, setAppTenantId] = useState<string | null>(null); // App tenant ID
  const [testResult, setTestResult] = useState<string | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [existingIntegration, setExistingIntegration] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [authenticationMode, setAuthenticationMode] = useState<'user' | 'app'>('app');
  const [showWizard, setShowWizard] = useState<boolean>(false);
  const [showGuide, setShowGuide] = useState<boolean>(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchAppTenantId = async () => {
      try {
        if (!user) {
          return;
        }

        // Get tenant ID from Auth0 user metadata (more reliable than database lookup)
        const tenantId = user.user_metadata?.tenant_id;

        if (!tenantId) {
          setTestResult('Error: No tenant ID associated with your account.');
          return;
        }

        setAppTenantId(tenantId);

        // Now check if there's an existing integration for this tenant
        fetchExistingIntegration(tenantId);
      } catch (err) {
        console.error('Unexpected error in fetchAppTenantId:', err);
        setTestResult('Error: An unexpected error occurred while fetching your tenant ID.');
      }
    };

    const fetchExistingIntegration = async (rolewise_tenant_id: string) => {
      try {
        const integration = await supabaseService.getIntegration(rolewise_tenant_id);

        if (integration) {
          setExistingIntegration(true);

          // Populate the form with existing values - app_tenant_id is the Entra ID tenant ID
          setTenantId(integration.app_tenant_id || '');
          setClientId(integration.client_id || '');
          // We don't populate the client secret for security reasons
        } else {
          setExistingIntegration(false);
        }
      } catch (err) {
        console.error('Unexpected error checking for existing integration:', err);
        setExistingIntegration(false);
      }
    };

    fetchAppTenantId();
  }, [user]);

  // Handle callback messages from integration authentication
  useEffect(() => {
    if (location.state) {
      const { authSuccess, message, error } = location.state as any;

      if (authSuccess && message) {
        setTestResult(message);
        integrationActions.setError(null);
      } else if (error) {
        setTestResult(`Error: ${error}`);
        integrationActions.setError(error);
      }

      // Clear the location state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state, integrationActions]);

  // Wizard handlers
  const handleWizardComplete = async (config: IntegrationConfig) => {
    setTenantId(config.tenantId);
    setClientId(config.clientId);
    setClientSecret(config.clientSecret || '');
    setShowWizard(false);

    // Automatically save the integration after wizard completion
    await saveIntegrationWithConfig(config);
  };

  const handleWizardCancel = () => {
    setShowWizard(false);
  };

  const saveIntegrationWithConfig = async (config: IntegrationConfig) => {
    if (!appTenantId) {
      setTestResult('Error: Could not determine your Rolewise tenant ID.');
      return;
    }

    // Validate configuration
    const validation = validateIntegrationConfig(config, authenticationMode, existingIntegration);
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setValidationErrors(errorMap);
      setTestResult('Please fix the validation errors before saving.');
      return;
    }

    setValidationErrors({});
    setLocalLoading(true);

    try {
      const integrationData: any = {
        app_tenant_id: config.tenantId,
        client_id: config.clientId,
      };

      if (config.clientSecret) {
        integrationData.client_secret = config.clientSecret;
      }

      await supabaseService.saveIntegration(appTenantId, integrationData);

      setExistingIntegration(true);
      setIsEditMode(false);
      setTestResult(`Integration ${existingIntegration ? 'updated' : 'created'} successfully!`);
    } catch (error) {
      console.error('Error saving integration:', error);
      setTestResult('Error: Failed to save integration.');
    } finally {
      setLocalLoading(false);
    }
  };

  const saveIntegration = async () => {
    const config: IntegrationConfig = {
      tenantId,
      clientId,
      clientSecret
    };

    await saveIntegrationWithConfig(config);
  };

  const testConnection = async () => {
    if (!tenantId || !clientId || !appTenantId) {
      setTestResult('Error: Tenant ID and Client ID are required.');
      return;
    }

    if (!clientSecret && !existingIntegration && authenticationMode === 'app') {
      setTestResult('Error: Client Secret is required for app authentication.');
      return;
    }

    setLocalLoading(true);
    integrationActions.setError(null);

    try {
      const config = {
        tenantId,
        clientId,
        clientSecret: authenticationMode === 'app' ? clientSecret : undefined
      };

      console.log(`[Integration Setup] Testing connection with ${authenticationMode} authentication`);

      if (authenticationMode === 'user') {
        // Use user authentication flow (MSAL popup)
        const success = await integrationActions.authenticateIntegration(config);
        if (success) {
          setTestResult('User authentication successful! You can now save this integration.');
        } else {
          setTestResult('User authentication failed. Please try again.');
        }
      } else {
        // Use app authentication flow (client credentials)
        const success = await integrationActions.testConnection(config);
        if (success) {
          setTestResult('App authentication successful! Connection verified.');
        } else {
          setTestResult('App authentication failed. Please check your credentials.');
        }
      }
    } catch (error: any) {
      console.error('Error testing connection:', error);
      setTestResult(`Error: ${error.message || 'Failed to test connection'}`);
      integrationActions.setError(error.message || 'Connection test failed');
    } finally {
      setLocalLoading(false);
    }
  };

  // Authentication check is handled by the Auth0 context and API client

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Integration Setup</h1>

      {contextError && !existingIntegration && (
        <div className={styles.error}>
          <strong>Error:</strong> {contextError}
        </div>
      )}

      <div className={styles.content}>
        {contextLoading && <p className={styles.loading}>Loading from context...</p>}
        {localLoading && <p className={styles.loading}>Loading locally...</p>}

        <div className={styles.formGroup}>
          <p>
            {existingIntegration
              ? 'Your Microsoft Entra ID integration is configured. You can update it below.'
              : 'Configure your Microsoft Entra ID integration below to connect your tenant with Rolewise.ai.'}
          </p>

          <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem', flexWrap: 'wrap' }}>
            {!existingIntegration && (
              <button
                onClick={() => setShowWizard(true)}
                className={styles.button}
                style={{ backgroundColor: '#4caf50' }}
              >
                🧙‍♂️ Setup Wizard
              </button>
            )}

            <button
              onClick={() => setShowGuide(true)}
              className={styles.button}
              style={{ backgroundColor: '#2196F3' }}
            >
              📖 Setup Guide
            </button>

            {existingIntegration && !isEditMode && (
              <button
                onClick={() => setIsEditMode(true)}
                className={styles.button}
              >
                ✏️ Edit Configuration
              </button>
            )}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Entra ID Tenant ID: <span style={{ color: 'red' }}>*</span>
            <input
              type="text"
              value={tenantId}
              onChange={(e) => {
                setTenantId(e.target.value);
                // Clear validation error when user starts typing
                if (validationErrors.tenantId) {
                  setValidationErrors(prev => ({ ...prev, tenantId: '' }));
                }
              }}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={`${styles.input} ${validationErrors.tenantId ? styles.inputError : ''}`}
              placeholder="12345678-1234-1234-1234-123456789abc"
            />
          </label>
          {validationErrors.tenantId && (
            <div style={{ color: '#f44336', fontSize: '12px', marginTop: '4px' }}>
              {validationErrors.tenantId}
            </div>
          )}
          <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
            Your Entra ID tenant GUID (found in Azure Portal → Entra ID → Overview)
          </small>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Client ID: <span style={{ color: 'red' }}>*</span>
            <input
              type="text"
              value={clientId}
              onChange={(e) => {
                setClientId(e.target.value);
                // Clear validation error when user starts typing
                if (validationErrors.clientId) {
                  setValidationErrors(prev => ({ ...prev, clientId: '' }));
                }
              }}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={`${styles.input} ${validationErrors.clientId ? styles.inputError : ''}`}
              placeholder="12345678-1234-1234-1234-123456789abc"
            />
          </label>
          {validationErrors.clientId && (
            <div style={{ color: '#f44336', fontSize: '12px', marginTop: '4px' }}>
              {validationErrors.clientId}
            </div>
          )}
          <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
            Application (client) ID from your app registration
          </small>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Authentication Mode:
            <select
              value={authenticationMode}
              onChange={(e) => setAuthenticationMode(e.target.value as 'user' | 'app')}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
            >
              <option value="app">App Authentication (Client Credentials)</option>
              <option value="user">User Authentication (Interactive Login)</option>
            </select>
          </label>
          <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
            {authenticationMode === 'app'
              ? 'Uses client secret for server-to-server authentication'
              : 'Requires interactive login to your Entra ID tenant'
            }
          </small>
        </div>

        {authenticationMode === 'app' && (
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Client Secret: {!existingIntegration && <span style={{ color: 'red' }}>*</span>}
              <input
                type="password"
                value={clientSecret}
                onChange={(e) => {
                  setClientSecret(e.target.value);
                  // Clear validation error when user starts typing
                  if (validationErrors.clientSecret) {
                    setValidationErrors(prev => ({ ...prev, clientSecret: '' }));
                  }
                }}
                disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
                className={`${styles.input} ${validationErrors.clientSecret ? styles.inputError : ''}`}
                placeholder={existingIntegration ? "Enter new Client Secret (leave blank to keep current)" : "Enter your Client Secret"}
              />
            </label>
            {validationErrors.clientSecret && (
              <div style={{ color: '#f44336', fontSize: '12px', marginTop: '4px' }}>
                {validationErrors.clientSecret}
              </div>
            )}
            <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
              Client secret from your app registration (Certificates & secrets)
            </small>
          </div>
        )}

        <div className={styles.buttonGroup}>
          {(!existingIntegration || isEditMode) && (
            <button
              onClick={saveIntegration}
              disabled={localLoading || contextLoading || !appTenantId}
              className={styles.button}
            >
              {localLoading ? 'Saving...' : existingIntegration ? 'Update Integration' : 'Save Integration'}
            </button>
          )}

          {isEditMode && (
            <button
              onClick={() => setIsEditMode(false)}
              disabled={localLoading || contextLoading}
              className={styles.button}
            >
              Cancel
            </button>
          )}

          <button
            onClick={testConnection}
            disabled={
              localLoading ||
              contextLoading ||
              !tenantId ||
              !clientId ||
              (authenticationMode === 'app' && !clientSecret && !existingIntegration) ||
              !appTenantId
            }
            className={styles.button}
          >
            {localLoading ? 'Testing...' : `Test ${authenticationMode === 'user' ? 'User Authentication' : 'Connection'}`}
          </button>
        </div>

        {/* Integration authentication status */}
        {integrationState.isAuthenticated && (
          <div className={styles.success}>
            ✅ Authenticated to {integrationState.accountInfo?.username || 'Entra ID tenant'}
            <button
              onClick={integrationActions.clearAuthentication}
              style={{
                marginLeft: '10px',
                padding: '4px 8px',
                fontSize: '12px',
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Clear Auth
            </button>
          </div>
        )}

        {integrationState.error && (
          <div className={styles.error}>
            Integration Error: {integrationState.error}
          </div>
        )}

        {testResult && (
          <div className={`${styles.result} ${testResult.includes('Error') ? styles.error : styles.success}`}>
            {testResult}
          </div>
        )}

        {/* Wizard Modal */}
        {showWizard && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <IntegrationWizard
              onComplete={handleWizardComplete}
              onCancel={handleWizardCancel}
              existingConfig={existingIntegration ? { tenantId, clientId, clientSecret } : undefined}
              isEditMode={isEditMode}
            />
          </div>
        )}

        {/* Setup Guide Modal */}
        {showGuide && (
          <IntegrationSetupGuide onClose={() => setShowGuide(false)} />
        )}
      </div>
    </div>
  );
};

// Wrapper component that provides the Entra ID integration context
const IntegrationSetup: React.FC = () => {
  return (
    <EntraIdIntegrationProvider>
      <IntegrationSetupContent />
    </EntraIdIntegrationProvider>
  );
};

export default IntegrationSetup;