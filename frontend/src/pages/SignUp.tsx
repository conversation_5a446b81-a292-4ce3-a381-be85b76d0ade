import { Navigate } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import * as styles from './Login.css'; // Reuse the login styles

const SignUpPage: React.FC = () => {
  const { isLoading, isAuthenticated, loginWithRedirect, error } = useAuth0();

  // Redirect if already logged in
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.card}>
          <h1>Loading...</h1>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.card}>
          <h1>Authentication Error</h1>
          <p>{error.message}</p>
          <button onClick={() => loginWithRedirect({ authorizationParams: { screen_hint: 'signup' } })}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <h1>Sign Up for Rolewise.ai</h1>
        <div className={styles.clerkContainer}>
          <p>Join Rolewise.ai to get started with role-based access management.</p>
          <button
            onClick={() => loginWithRedirect({
              authorizationParams: {
                screen_hint: 'signup'
              }
            })}
            className={styles.button}
          >
            Sign Up with Auth0
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
