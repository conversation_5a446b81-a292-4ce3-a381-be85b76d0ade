import { Navigate } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import * as styles from './Login.css';

const Login: React.FC = () => {
  const { isLoading, isAuthenticated, loginWithRedirect, error } = useAuth0();

  // Redirect if already logged in
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.card}>
          <h1>Loading...</h1>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.card}>
          <h1>Authentication Error</h1>
          <p>{error.message}</p>
          <button onClick={() => loginWithRedirect()}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <h1>Login to Rolewise.ai</h1>
        <div className={styles.clerkContainer}>
          <p>Welcome to Rolewise.ai. Please log in to continue.</p>
          <button
            onClick={() => loginWithRedirect()}
            className={styles.button}
          >
            Log In with Auth0
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;