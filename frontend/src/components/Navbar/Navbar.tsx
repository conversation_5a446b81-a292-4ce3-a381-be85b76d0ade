import React from 'react';
import { AuthButtons, useAuthStatus } from '../Auth0Auth';
import * as styles from './Navbar.css';

const Navbar = () => {
  const { isAuthenticated, userDetails } = useAuthStatus();

  return (
    <nav className={styles.navbarStyle}>
      <div className={styles.navbarLeftStyle}>
        <div className={styles.logoContainerStyle}>
          <span className={styles.logoTextStyle}>R</span>
        </div>
        <span className={styles.userNameStyle}>Rolewise.ai</span>
      </div>

      <div className={styles.navbarRightStyle}>
        <div className={styles.userButtonContainer}>
          {isAuthenticated && (
            <>
              <span className={styles.emailTextStyle}>
                {userDetails?.email || userDetails?.name || 'User'}
              </span>
              <AuthButtons />
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;