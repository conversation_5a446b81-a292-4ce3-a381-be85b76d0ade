import { useState, useEffect } from 'react';

import { edgeFunctions } from '../../services/api/apiClient';
import { useAuthStore } from '../../stores/authStore';
import {
  webhookManagerContainer,
  webhookListContainer,
  webhookCard,
  webhookHeader,
  webhookBody,
  webhookFooter,
  webhookStatus,
  webhookStatusActive,
  webhookStatusInactive,
  webhookForm,
  formGroup,
  formLabel,
  formInput,
  formSelect,
  formCheckbox,
  formButton,
  formButtonPrimary,
  formButtonSecondary,
  formButtonDanger,
  deliveriesTable,
  deliveryStatus,
  deliveryStatusSuccess,
  deliveryStatusFailed,
  deliveryStatusPending,
  modalOverlay,
  modalContent,
  modalHeader,
  modalBody,
  modalFooter,
  secretDisplay,
} from './WebhookManager.css';

interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  secret?: string;
}

interface WebhookDelivery {
  id: string;
  event_type: string;
  status: 'pending' | 'success' | 'failed';
  status_code: number | null;
  error: string | null;
  attempt_count: number;
  created_at: string;
  updated_at: string;
}

interface WebhookFormData {
  id?: string;
  name: string;
  url: string;
  events: string[];
  is_active: boolean;
  secret?: string;
}

const WebhookManager: React.FC = () => {
  const { user, tenantId } = useAuthStore();
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);
  const [availableEvents, setAvailableEvents] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<WebhookFormData>({
    name: '',
    url: '',
    events: [],
    is_active: true,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showSecret, setShowSecret] = useState(false);
  const [newSecret, setNewSecret] = useState<string | null>(null);
  const [selectedWebhook, setSelectedWebhook] = useState<string | null>(null);
  const [deliveries, setDeliveries] = useState<WebhookDelivery[]>([]);
  const [showDeliveries, setShowDeliveries] = useState(false);

  // Fetch webhooks when component mounts
  useEffect(() => {
    if (tenantId) {
      fetchWebhooks();
      fetchAvailableEvents();
    }
  }, [tenantId]);

  // Fetch webhooks for the current tenant
  const fetchWebhooks = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session?.access_token) {
        throw new Error('Authentication failed: Unable to obtain valid session');
      }
      const accessToken = sessionData.session.access_token;

      const { data, error } = await supabase.functions.invoke('manage-webhooks', {
        body: { action: 'list', tenantId },
        headers: { Authorization: `Bearer ${accessToken}` },
        query: { tenantId },
        method: 'GET',
      });

      if (error) {
        throw new Error(`Failed to fetch webhooks: ${error.message}`);
      }

      setWebhooks(data.webhooks || []);
    } catch (error) {
      console.error('Error fetching webhooks:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch webhooks');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch available webhook event types
  const fetchAvailableEvents = async () => {
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session?.access_token) {
        throw new Error('Authentication failed: Unable to obtain valid session');
      }
      const accessToken = sessionData.session.access_token;

      const { data, error } = await supabase.functions.invoke('manage-webhooks', {
        body: { action: 'events' },
        headers: { Authorization: `Bearer ${accessToken}` },
        method: 'GET',
      });

      if (error) {
        throw new Error(`Failed to fetch event types: ${error.message}`);
      }

      setAvailableEvents(data.events || []);
    } catch (error) {
      console.error('Error fetching event types:', error);
    }
  };

  // Fetch webhook deliveries
  const fetchDeliveries = async (webhookId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await edgeFunctions.manageWebhooks('deliveries', {
        webhookId,
        limit: '20'
      });

      if (!response.success) {
        throw new Error(`Failed to fetch webhook deliveries: ${response.error}`);
      }

      setDeliveries(response.data?.deliveries || []);
      setSelectedWebhook(webhookId);
      setShowDeliveries(true);
    } catch (error) {
      console.error('Error fetching webhook deliveries:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch webhook deliveries');
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new webhook
  const createWebhook = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session?.access_token) {
        throw new Error('Authentication failed: Unable to obtain valid session');
      }
      const accessToken = sessionData.session.access_token;

      const { data, error } = await supabase.functions.invoke('manage-webhooks', {
        body: {
          ...formData,
          tenant_id: tenantId,
        },
        headers: { Authorization: `Bearer ${accessToken}` },
        method: 'POST',
      });

      if (error) {
        throw new Error(`Failed to create webhook: ${error.message}`);
      }

      // Show the secret
      setNewSecret(data.webhook.secret);
      setShowSecret(true);

      // Reset form and refresh webhooks
      resetForm();
      fetchWebhooks();
    } catch (error) {
      console.error('Error creating webhook:', error);
      setError(error instanceof Error ? error.message : 'Failed to create webhook');
    } finally {
      setIsLoading(false);
    }
  };

  // Update an existing webhook
  const updateWebhook = async () => {
    if (!formData.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session?.access_token) {
        throw new Error('Authentication failed: Unable to obtain valid session');
      }
      const accessToken = sessionData.session.access_token;

      const { data, error } = await supabase.functions.invoke('manage-webhooks', {
        body: formData,
        headers: { Authorization: `Bearer ${accessToken}` },
        method: 'PUT',
      });

      if (error) {
        throw new Error(`Failed to update webhook: ${error.message}`);
      }

      // Show the new secret if one was generated
      if (data.webhook.secret) {
        setNewSecret(data.webhook.secret);
        setShowSecret(true);
      }

      // Reset form and refresh webhooks
      resetForm();
      fetchWebhooks();
    } catch (error) {
      console.error('Error updating webhook:', error);
      setError(error instanceof Error ? error.message : 'Failed to update webhook');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a webhook
  const deleteWebhook = async (webhookId: string) => {
    if (!confirm('Are you sure you want to delete this webhook?')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData.session?.access_token) {
        throw new Error('Authentication failed: Unable to obtain valid session');
      }
      const accessToken = sessionData.session.access_token;

      const { error } = await supabase.functions.invoke('manage-webhooks', {
        headers: { Authorization: `Bearer ${accessToken}` },
        query: { id: webhookId },
        method: 'DELETE',
      });

      if (error) {
        throw new Error(`Failed to delete webhook: ${error.message}`);
      }

      // Refresh webhooks
      fetchWebhooks();
    } catch (error) {
      console.error('Error deleting webhook:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete webhook');
    } finally {
      setIsLoading(false);
    }
  };

  // Reset the form
  const resetForm = () => {
    setFormData({
      name: '',
      url: '',
      events: [],
      is_active: true,
    });
    setIsEditing(false);
    setShowForm(false);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle event selection changes
  const handleEventChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = e.target.options;
    const selectedEvents = [];
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        selectedEvents.push(options[i].value);
      }
    }
    setFormData((prev) => ({ ...prev, events: selectedEvents }));
  };

  // Edit a webhook
  const editWebhook = (webhook: Webhook) => {
    setFormData({
      id: webhook.id,
      name: webhook.name,
      url: webhook.url,
      events: webhook.events,
      is_active: webhook.is_active,
    });
    setIsEditing(true);
    setShowForm(true);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className={webhookManagerContainer}>
      <h2>Webhook Management</h2>
      
      {error && <div className="error-message">{error}</div>}
      
      <div className="actions">
        <button 
          onClick={() => { resetForm(); setShowForm(true); }}
          disabled={isLoading}
          className={formButtonPrimary}
        >
          Create New Webhook
        </button>
      </div>
      
      {showForm && (
        <div className={webhookForm}>
          <h3>{isEditing ? 'Edit Webhook' : 'Create New Webhook'}</h3>
          
          <div className={formGroup}>
            <label className={formLabel}>Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={formInput}
              placeholder="Webhook Name"
              required
            />
          </div>
          
          <div className={formGroup}>
            <label className={formLabel}>URL (HTTPS only)</label>
            <input
              type="url"
              name="url"
              value={formData.url}
              onChange={handleInputChange}
              className={formInput}
              placeholder="https://example.com/webhook"
              required
            />
          </div>
          
          <div className={formGroup}>
            <label className={formLabel}>Events (Hold Ctrl/Cmd to select multiple)</label>
            <select
              name="events"
              multiple
              value={formData.events}
              onChange={handleEventChange}
              className={formSelect}
              required
            >
              {availableEvents.map((event) => (
                <option key={event} value={event}>
                  {event}
                </option>
              ))}
            </select>
          </div>
          
          <div className={formGroup}>
            <label className={formCheckbox}>
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleCheckboxChange}
              />
              Active
            </label>
          </div>
          
          {isEditing && (
            <div className={formGroup}>
              <label className={formCheckbox}>
                <input
                  type="checkbox"
                  name="regenerateSecret"
                  onChange={(e) => {
                    setFormData((prev) => ({
                      ...prev,
                      secret: e.target.checked ? 'generate_new' : undefined,
                    }));
                  }}
                />
                Generate New Secret
              </label>
            </div>
          )}
          
          <div className={webhookFooter}>
            <button
              onClick={resetForm}
              className={formButtonSecondary}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={isEditing ? updateWebhook : createWebhook}
              className={formButtonPrimary}
              disabled={isLoading || !formData.name || !formData.url || formData.events.length === 0}
            >
              {isLoading ? 'Processing...' : isEditing ? 'Update Webhook' : 'Create Webhook'}
            </button>
          </div>
        </div>
      )}
      
      {showSecret && newSecret && (
        <div className={modalOverlay}>
          <div className={modalContent}>
            <div className={modalHeader}>
              <h3>Webhook Secret</h3>
            </div>
            <div className={modalBody}>
              <p>
                This is your webhook secret. It will be used to sign webhook payloads.
                <strong>Save this secret now as it will not be shown again.</strong>
              </p>
              <div className={secretDisplay}>
                {newSecret}
              </div>
            </div>
            <div className={modalFooter}>
              <button
                onClick={() => {
                  setShowSecret(false);
                  setNewSecret(null);
                }}
                className={formButtonPrimary}
              >
                I've Saved the Secret
              </button>
            </div>
          </div>
        </div>
      )}
      
      {showDeliveries && (
        <div className={modalOverlay}>
          <div className={modalContent}>
            <div className={modalHeader}>
              <h3>Webhook Deliveries</h3>
            </div>
            <div className={modalBody}>
              {deliveries.length === 0 ? (
                <p>No deliveries found for this webhook.</p>
              ) : (
                <table className={deliveriesTable}>
                  <thead>
                    <tr>
                      <th>Event</th>
                      <th>Status</th>
                      <th>Attempts</th>
                      <th>Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    {deliveries.map((delivery) => (
                      <tr key={delivery.id}>
                        <td>{delivery.event_type}</td>
                        <td>
                          <span
                            className={`${deliveryStatus} ${
                              delivery.status === 'success'
                                ? deliveryStatusSuccess
                                : delivery.status === 'failed'
                                ? deliveryStatusFailed
                                : deliveryStatusPending
                            }`}
                          >
                            {delivery.status}
                            {delivery.status_code && ` (${delivery.status_code})`}
                          </span>
                        </td>
                        <td>{delivery.attempt_count}</td>
                        <td>{formatDate(delivery.created_at)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            <div className={modalFooter}>
              <button
                onClick={() => setShowDeliveries(false)}
                className={formButtonPrimary}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className={webhookListContainer}>
        <h3>Your Webhooks</h3>
        
        {isLoading && <p>Loading webhooks...</p>}
        
        {!isLoading && webhooks.length === 0 && (
          <p>No webhooks found. Create your first webhook to receive notifications.</p>
        )}
        
        {webhooks.map((webhook) => (
          <div key={webhook.id} className={webhookCard}>
            <div className={webhookHeader}>
              <h4>{webhook.name}</h4>
              <span
                className={`${webhookStatus} ${
                  webhook.is_active ? webhookStatusActive : webhookStatusInactive
                }`}
              >
                {webhook.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            <div className={webhookBody}>
              <p><strong>URL:</strong> {webhook.url}</p>
              <p><strong>Events:</strong> {webhook.events.join(', ')}</p>
              <p><strong>Created:</strong> {formatDate(webhook.created_at)}</p>
              <p><strong>Last Updated:</strong> {formatDate(webhook.updated_at)}</p>
            </div>
            
            <div className={webhookFooter}>
              <button
                onClick={() => fetchDeliveries(webhook.id)}
                className={formButtonSecondary}
                disabled={isLoading}
              >
                View Deliveries
              </button>
              <button
                onClick={() => editWebhook(webhook)}
                className={formButtonSecondary}
                disabled={isLoading}
              >
                Edit
              </button>
              <button
                onClick={() => deleteWebhook(webhook.id)}
                className={formButtonDanger}
                disabled={isLoading}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WebhookManager;
