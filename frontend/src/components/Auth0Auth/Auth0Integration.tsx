import { useEffect, useRef } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useAuthStore, initializeUserProfile, clearAuthState } from '../../stores/authStore';
import { AuthStatus } from '../../types/auth';

/**
 * Component that synchronizes Auth0 state with our Zustand auth store
 * This should be rendered at the top level of the app (in main.tsx)
 */
export const Auth0Integration: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { 
    isLoading, 
    isAuthenticated, 
    user, 
    getAccessTokenSilently,
    error 
  } = useAuth0();
  
  const { status, setStatus } = useAuthStore();
  const initializingRef = useRef(false);

  useEffect(() => {
    const syncAuthState = async () => {
      // Prevent duplicate initialization attempts
      if (initializingRef.current) return;

      // Handle loading state
      if (isLoading) {
        setStatus(AuthStatus.LOADING);
        return;
      }

      // Handle authentication errors
      if (error) {
        console.error('Auth0 error:', error);
        setStatus(AuthStatus.UNAUTHENTICATED);
        clearAuthState();
        return;
      }

      // Handle authenticated user
      if (isAuthenticated && user) {
        // Avoid redundant initialization if already authenticated
        if (status === AuthStatus.AUTHENTICATED) {
          return;
        }

        initializingRef.current = true;
        
        try {
          setStatus(AuthStatus.LOADING);
          
          // Get access token with smart caching
          let accessToken: string;
          try {
            accessToken = await getAccessTokenSilently({
              cacheMode: 'cache-only',
            });
          } catch (cacheError: any) {
            // If cache miss, fetch fresh token
            if (cacheError.error === 'cache_miss' || cacheError.error === 'missing_refresh_token') {
              accessToken = await getAccessTokenSilently();
            } else {
              throw cacheError;
            }
          }
          
          // Initialize user profile and sync with store
          await initializeUserProfile(user, accessToken);
          
        } catch (tokenError: any) {
          console.error('Error during authentication setup:', tokenError);
          
          // Handle specific Auth0 errors
          if (tokenError.error === 'consent_required') {
            console.log('User consent required, redirecting to Auth0...');
            // Could trigger loginWithRedirect here if needed
          } else if (tokenError.error === 'login_required') {
            console.log('Login required, user will be redirected');
          }
          
          setStatus(AuthStatus.UNAUTHENTICATED);
          clearAuthState();
        } finally {
          initializingRef.current = false;
        }
      } else {
        // User is not authenticated
        if (status !== AuthStatus.UNAUTHENTICATED) {
          setStatus(AuthStatus.UNAUTHENTICATED);
          clearAuthState();
        }
      }
    };

    syncAuthState();
  }, [
    isLoading, 
    isAuthenticated, 
    user?.sub, // Only re-run if user changes (using sub as unique identifier)
    error, 
    getAccessTokenSilently, 
    setStatus, 
    status
  ]);

  // Reset initialization flag when user changes
  useEffect(() => {
    initializingRef.current = false;
  }, [user?.sub]);

  return <>{children}</>;
};

export default Auth0Integration;