import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import { useAuthStore } from '../../stores/authStore';
import { AuthStatus } from '../../types/auth';
import * as styles from './Auth0Auth.css';

/**
 * Component that requires authentication to access
 * Redirects to login page if user is not authenticated
 */
export const RequireAuth: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoading, isAuthenticated, error } = useAuth0();
  const { status } = useAuthStore();

  // Show loading state - either Auth0 is loading OR our store is initializing
  if (isLoading || status === AuthStatus.LOADING) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px',
        color: '#666'
      }}>
        <div>Loading authentication...</div>
      </div>
    );
  }

  // Handle authentication errors
  if (error) {
    console.error('Auth0 error:', error);
    return (
      <div style={{
        maxWidth: '500px',
        margin: '50px auto',
        padding: '20px',
        backgroundColor: '#ffebee',
        border: '1px solid #ffcdd2',
        borderRadius: '8px',
        textAlign: 'center'
      }}>
        <h2>Authentication Error</h2>
        <p>We encountered an issue while trying to authenticate you.</p>
        <details style={{ marginTop: '10px', textAlign: 'left' }}>
          <summary>Error details</summary>
          <pre style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px', 
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {error.message}
          </pre>
        </details>
        <button 
          onClick={() => window.location.reload()}
          style={{
            marginTop: '15px',
            padding: '8px 16px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Try Again
        </button>
      </div>
    );
  }

  // Check both Auth0 authentication and our store status
  if (!isAuthenticated || status === AuthStatus.UNAUTHENTICATED) {
    return <Navigate to="/login" replace />;
  }

  // Only render children if fully authenticated
  if (status === AuthStatus.AUTHENTICATED) {
    return <>{children}</>;
  }

  // Fallback loading state
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      fontSize: '16px',
      color: '#666'
    }}>
      <div>Initializing...</div>
    </div>
  );
};

/**
 * Authentication buttons for the navbar
 * This replaces the direct Auth0 calls in your current Navbar
 */
export const AuthButtons = () => {
  const { isLoading, isAuthenticated, loginWithRedirect, logout } = useAuth0();
  const { user: storeUser, status } = useAuthStore();

  if (isLoading || status === AuthStatus.LOADING) {
    return <div style={{ color: '#666', fontSize: '14px' }}>Loading...</div>;
  }

  return (
    <div className={styles.authButtonsContainer}>
      {!isAuthenticated ? (
        <button 
          onClick={() => loginWithRedirect()}
          className={styles.authButton}
        >
          Log In
        </button>
      ) : (
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
          {storeUser && (
            <span style={{ fontSize: '14px', color: '#666' }}>
              {storeUser.name || storeUser.email || 'User'}
            </span>
          )}
          <button 
            onClick={() => logout({ 
              logoutParams: { 
                returnTo: window.location.origin + '/login' 
              } 
            })}
            className={styles.authButton}
          >
            Log Out
          </button>
        </div>
      )}
    </div>
  );
};

/**
 * Hook to get current authentication status and user info
 * This provides a clean interface for components to access auth state
 */
export const useAuthStatus = () => {
  const { isLoading, isAuthenticated, error, getAccessTokenSilently } = useAuth0();
  const { user, status, userDetails, permissions, tenantId } = useAuthStore();
  
  return {
    // Auth0 state
    isLoading: isLoading || status === AuthStatus.LOADING,
    isAuthenticated: isAuthenticated && status === AuthStatus.AUTHENTICATED,
    error,
    getAccessToken: getAccessTokenSilently,
    
    // Enhanced user data from store
    user,
    userDetails,
    permissions,
    tenantId,
    status
  };
};

export default {
  RequireAuth,
  AuthButtons,
  useAuthStatus
};