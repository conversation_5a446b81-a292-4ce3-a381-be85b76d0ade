import { style, keyframes } from '@vanilla-extract/css';

const spin = keyframes({
  from: { transform: 'rotate(0deg)' },
  to: { transform: 'rotate(360deg)' },
});

export const layoutContainer = style({
  display: 'flex',
  flexDirection: 'column',
  minHeight: '100vh',
  backgroundColor: '#f3f4f6',
});

export const contentContainer = style({
  flex: 1,
  marginLeft: '16rem', // Match sidebar width
  marginTop: '4rem', // Match navbar height
  padding: '2rem',
  transition: 'margin-left 0.3s ease',
  '@media': {
    'screen and (max-width: 768px)': {
      marginLeft: '0',
    },
  },
});

// Style for when sidebar is collapsed
export const contentContainerExpanded = style({
  marginLeft: '4rem', // Match collapsed sidebar width
  '@media': {
    'screen and (max-width: 768px)': {
      marginLeft: '0',
    },
  },
});

export const navContainer = style({
  width: '100%',
  position: 'sticky',
  top: 0,
  zIndex: 50,
  backgroundColor: 'white',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  height: '4rem', // Fixed height for navbar
});

export const main = style({
  flex: 1,
  padding: '1rem',
  backgroundColor: '#f9fafb',
  width: '100%',
  '@media': {
    'screen and (min-width: 768px)': {
      padding: '1.5rem',
    },
  },
});

export const loading = style({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
  backgroundColor: '#f9fafb',
});

export const mainContentStyle = style({
  flex: 1, // Take remaining width next to the sidebar
  display: 'flex',
  flexDirection: 'column', // Stack navbar and main content vertically
  width: '100%', // Ensure this takes full available width
  overflowX: 'hidden', // Prevent horizontal scrolling
});

export const mainStyle = style({
  flex: 1, // Take remaining height below the navbar
  padding: '1rem', // Add padding for spacing
  paddingTop: '1rem', // Reduce top padding since navbar is now sticky
  width: '100%', // Ensure content takes full width
});

export const spinnerStyle = style({
  display: 'inline-block',
  width: '2rem',
  height: '2rem',
  border: '3px solid #f3f4f6',
  borderTop: '3px solid #3b82f6',
  borderRadius: '50%',
  animation: 'spin 1s linear infinite',
});

export const errorMessageStyle = style({
  padding: '1.5rem',
  margin: '1rem 0',
  backgroundColor: '#fee2e2',
  border: '1px solid #ef4444',
  borderRadius: '0.375rem',
  color: '#991b1b',
});

export const signOutButtonStyle = style({
  backgroundColor: '#3b82f6',
  color: 'white',
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  fontWeight: '500',
  cursor: 'pointer',
  marginTop: '1rem',
  ':hover': {
    backgroundColor: '#2563eb',
  },
});