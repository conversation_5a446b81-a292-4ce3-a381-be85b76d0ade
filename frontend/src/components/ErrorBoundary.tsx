import React, { Component, ErrorInfo, ReactNode } from 'react';
import { useNotificationStore } from '../stores/notificationStore';
import { useAppStateStore } from '../stores/appStateStore';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  context?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId?: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Add error to app state
    const appStateStore = useAppStateStore.getState();
    const notificationStore = useNotificationStore.getState();

    const errorId = appStateStore.addError({
      code: 'REACT_ERROR_BOUNDARY',
      message: 'Application Error',
      details: `${error.message}\n\nComponent Stack:\n${errorInfo.componentStack}`,
      context: this.props.context || 'application',
      recoverable: true,
      maxRetries: 1
    });

    // Show error notification
    notificationStore.showError(
      'Application Error',
      'An unexpected error occurred. Please try refreshing the page.',
      {
        context: this.props.context || 'application',
        persistent: true,
        actions: [
          {
            label: 'Retry',
            action: () => this.handleRetry(),
            variant: 'primary'
          },
          {
            label: 'Reload Page',
            action: () => window.location.reload(),
            variant: 'secondary'
          }
        ]
      }
    );

    this.setState({
      error,
      errorInfo,
      errorId
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: undefined
    });

    // Clear the error from app state
    if (this.state.errorId) {
      const appStateStore = useAppStateStore.getState();
      appStateStore.acknowledgeError(this.state.errorId);
    }
  };

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      return (
        <div style={{ 
          padding: '20px', 
          margin: '20px', 
          backgroundColor: '#fff1f0', 
          border: '1px solid #ffa39e',
          borderRadius: '4px'
        }}>
          <h2>Something went wrong</h2>
          <details style={{ whiteSpace: 'pre-wrap' }}>
            <summary>Error details</summary>
            <p>{this.state.error && this.state.error.toString()}</p>
            <p>Component Stack:</p>
            <pre>{this.state.errorInfo && this.state.errorInfo.componentStack}</pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
