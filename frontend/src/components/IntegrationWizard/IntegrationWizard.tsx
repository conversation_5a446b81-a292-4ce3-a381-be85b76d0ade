import React, { useState, useEffect } from 'react';
import { useEntraIdIntegration } from '../../contexts/EntraIdIntegrationContext';
import { IntegrationConfig } from '../../contexts/EntraIdIntegrationContext';

// Wizard step types
export type WizardStep = 'overview' | 'credentials' | 'permissions' | 'test' | 'complete';

interface IntegrationWizardProps {
  onComplete: (config: IntegrationConfig) => void;
  onCancel: () => void;
  existingConfig?: IntegrationConfig;
  isEditMode?: boolean;
}

// Required Microsoft Graph permissions for the integration
const REQUIRED_PERMISSIONS = [
  {
    permission: 'Application.Read.All',
    description: 'Read all applications in your directory',
    reason: 'To analyze which applications your users have access to'
  },
  {
    permission: 'Group.Read.All',
    description: 'Read all groups in your directory',
    reason: 'To understand group memberships and security groups'
  },
  {
    permission: 'User.Read.All',
    description: 'Read all users in your directory',
    reason: 'To analyze user access patterns and group memberships'
  },
  {
    permission: 'Directory.Read.All',
    description: 'Read directory data',
    reason: 'To access organizational structure and relationships'
  }
];

const IntegrationWizard: React.FC<IntegrationWizardProps> = ({
  onComplete,
  onCancel,
  existingConfig,
  isEditMode = false
}) => {
  const { state: integrationState, actions: integrationActions } = useEntraIdIntegration();
  
  const [currentStep, setCurrentStep] = useState<WizardStep>('overview');
  const [config, setConfig] = useState<IntegrationConfig>({
    tenantId: existingConfig?.tenantId || '',
    clientId: existingConfig?.clientId || '',
    clientSecret: existingConfig?.clientSecret || ''
  });
  const [authMode, setAuthMode] = useState<'app' | 'user'>('app');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Validation functions
  const validateTenantId = (tenantId: string): string | null => {
    if (!tenantId.trim()) return 'Tenant ID is required';
    
    // Check if it's a valid GUID format
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!guidRegex.test(tenantId.trim())) {
      return 'Tenant ID must be a valid GUID format (e.g., 12345678-1234-1234-1234-123456789abc)';
    }
    
    return null;
  };

  const validateClientId = (clientId: string): string | null => {
    if (!clientId.trim()) return 'Client ID is required';
    
    // Check if it's a valid GUID format
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!guidRegex.test(clientId.trim())) {
      return 'Client ID must be a valid GUID format (e.g., 12345678-1234-1234-1234-123456789abc)';
    }
    
    return null;
  };

  const validateClientSecret = (clientSecret: string): string | null => {
    if (authMode === 'app' && !clientSecret.trim() && !isEditMode) {
      return 'Client Secret is required for app authentication';
    }
    
    if (clientSecret.trim() && clientSecret.length < 10) {
      return 'Client Secret appears to be too short';
    }
    
    return null;
  };

  const validateCurrentStep = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (currentStep === 'credentials') {
      const tenantError = validateTenantId(config.tenantId);
      const clientError = validateClientId(config.clientId);
      const secretError = validateClientSecret(config.clientSecret || '');
      
      if (tenantError) errors.tenantId = tenantError;
      if (clientError) errors.clientId = clientError;
      if (secretError) errors.clientSecret = secretError;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (!validateCurrentStep()) return;
    
    const steps: WizardStep[] = ['overview', 'credentials', 'permissions', 'test', 'complete'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };

  const handleBack = () => {
    const steps: WizardStep[] = ['overview', 'credentials', 'permissions', 'test', 'complete'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };

  const handleTestConnection = async () => {
    if (!validateCurrentStep()) return;
    
    setIsLoading(true);
    try {
      const testConfig = {
        ...config,
        clientSecret: authMode === 'app' ? config.clientSecret : undefined
      };
      
      let success = false;
      if (authMode === 'user') {
        success = await integrationActions.authenticateIntegration(testConfig);
      } else {
        success = await integrationActions.testConnection(testConfig);
      }
      
      if (success) {
        setCurrentStep('complete');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplete = () => {
    onComplete(config);
  };

  const renderStepIndicator = () => {
    const steps = [
      { key: 'overview', label: 'Overview' },
      { key: 'credentials', label: 'Credentials' },
      { key: 'permissions', label: 'Permissions' },
      { key: 'test', label: 'Test' },
      { key: 'complete', label: 'Complete' }
    ];
    
    const currentIndex = steps.findIndex(step => step.key === currentStep);
    
    return (
      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '2rem' }}>
        {steps.map((step, index) => (
          <div key={step.key} style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: index <= currentIndex ? '#2196F3' : '#e0e0e0',
                color: index <= currentIndex ? 'white' : '#666',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                fontWeight: 'bold'
              }}
            >
              {index + 1}
            </div>
            <span
              style={{
                marginLeft: '8px',
                marginRight: '16px',
                color: index <= currentIndex ? '#2196F3' : '#666',
                fontSize: '14px',
                fontWeight: index === currentIndex ? 'bold' : 'normal'
              }}
            >
              {step.label}
            </span>
            {index < steps.length - 1 && (
              <div
                style={{
                  width: '24px',
                  height: '2px',
                  backgroundColor: index < currentIndex ? '#2196F3' : '#e0e0e0',
                  marginRight: '16px'
                }}
              />
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderOverviewStep = () => (
    <div style={{ textAlign: 'center', padding: '2rem' }}>
      <h2>Microsoft Entra ID Integration</h2>
      <p style={{ fontSize: '16px', lineHeight: '1.6', marginBottom: '2rem', color: '#666' }}>
        This wizard will help you set up a secure connection between Rolewise.ai and your Microsoft Entra ID tenant.
        We'll guide you through the process of configuring the necessary credentials and permissions.
      </p>
      
      <div style={{ backgroundColor: '#f5f5f5', padding: '1.5rem', borderRadius: '8px', marginBottom: '2rem' }}>
        <h3 style={{ marginBottom: '1rem' }}>What you'll need:</h3>
        <ul style={{ textAlign: 'left', lineHeight: '1.8' }}>
          <li>Your Entra ID Tenant ID</li>
          <li>An App Registration in your Entra ID tenant</li>
          <li>Client ID from the app registration</li>
          <li>Client Secret (for app authentication) or admin consent (for user authentication)</li>
        </ul>
      </div>
      
      <div style={{ backgroundColor: '#e3f2fd', padding: '1.5rem', borderRadius: '8px', border: '1px solid #2196F3' }}>
        <h4 style={{ color: '#1976d2', marginBottom: '0.5rem' }}>Security Note</h4>
        <p style={{ fontSize: '14px', color: '#1976d2' }}>
          All credentials are encrypted and stored securely. We only request the minimum permissions necessary 
          to analyze your directory structure for role recommendations.
        </p>
      </div>
    </div>
  );

  const renderCredentialsStep = () => (
    <div style={{ padding: '1rem' }}>
      <h2>Integration Credentials</h2>
      <p style={{ marginBottom: '2rem', color: '#666' }}>
        Enter your Microsoft Entra ID credentials to establish the connection.
      </p>

      {/* Authentication Mode Selection */}
      <div style={{ marginBottom: '2rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
          Authentication Mode:
        </label>
        <select
          value={authMode}
          onChange={(e) => setAuthMode(e.target.value as 'app' | 'user')}
          style={{
            width: '100%',
            padding: '12px',
            border: '1px solid #ccc',
            borderRadius: '6px',
            fontSize: '14px'
          }}
        >
          <option value="app">App Authentication (Client Credentials)</option>
          <option value="user">User Authentication (Interactive Login)</option>
        </select>
        <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
          {authMode === 'app'
            ? 'Uses client secret for server-to-server authentication'
            : 'Requires interactive login and admin consent'
          }
        </small>
      </div>

      {/* Tenant ID */}
      <div style={{ marginBottom: '1.5rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
          Tenant ID: <span style={{ color: 'red' }}>*</span>
        </label>
        <input
          type="text"
          value={config.tenantId}
          onChange={(e) => setConfig({ ...config, tenantId: e.target.value })}
          placeholder="12345678-1234-1234-1234-123456789abc"
          style={{
            width: '100%',
            padding: '12px',
            border: `1px solid ${validationErrors.tenantId ? '#f44336' : '#ccc'}`,
            borderRadius: '6px',
            fontSize: '14px'
          }}
        />
        {validationErrors.tenantId && (
          <div style={{ color: '#f44336', fontSize: '12px', marginTop: '4px' }}>
            {validationErrors.tenantId}
          </div>
        )}
        <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
          Your Entra ID tenant GUID (found in Azure Portal → Entra ID → Overview)
        </small>
      </div>

      {/* Client ID */}
      <div style={{ marginBottom: '1.5rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
          Client ID: <span style={{ color: 'red' }}>*</span>
        </label>
        <input
          type="text"
          value={config.clientId}
          onChange={(e) => setConfig({ ...config, clientId: e.target.value })}
          placeholder="12345678-1234-1234-1234-123456789abc"
          style={{
            width: '100%',
            padding: '12px',
            border: `1px solid ${validationErrors.clientId ? '#f44336' : '#ccc'}`,
            borderRadius: '6px',
            fontSize: '14px'
          }}
        />
        {validationErrors.clientId && (
          <div style={{ color: '#f44336', fontSize: '12px', marginTop: '4px' }}>
            {validationErrors.clientId}
          </div>
        )}
        <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
          Application (client) ID from your app registration
        </small>
      </div>

      {/* Client Secret (only for app auth) */}
      {authMode === 'app' && (
        <div style={{ marginBottom: '1.5rem' }}>
          <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
            Client Secret: {!isEditMode && <span style={{ color: 'red' }}>*</span>}
          </label>
          <input
            type="password"
            value={config.clientSecret || ''}
            onChange={(e) => setConfig({ ...config, clientSecret: e.target.value })}
            placeholder={isEditMode ? "Enter new secret (leave blank to keep current)" : "Enter your client secret"}
            style={{
              width: '100%',
              padding: '12px',
              border: `1px solid ${validationErrors.clientSecret ? '#f44336' : '#ccc'}`,
              borderRadius: '6px',
              fontSize: '14px'
            }}
          />
          {validationErrors.clientSecret && (
            <div style={{ color: '#f44336', fontSize: '12px', marginTop: '4px' }}>
              {validationErrors.clientSecret}
            </div>
          )}
          <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
            Client secret from your app registration (Certificates & secrets)
          </small>
        </div>
      )}

      {/* Help section */}
      <div style={{ backgroundColor: '#fff3e0', padding: '1rem', borderRadius: '6px', border: '1px solid #ff9800' }}>
        <h4 style={{ color: '#f57c00', marginBottom: '0.5rem' }}>Need help finding these values?</h4>
        <p style={{ fontSize: '14px', color: '#f57c00', marginBottom: '0.5rem' }}>
          1. Go to Azure Portal → Entra ID → App registrations
        </p>
        <p style={{ fontSize: '14px', color: '#f57c00', marginBottom: '0.5rem' }}>
          2. Select your app or create a new one
        </p>
        <p style={{ fontSize: '14px', color: '#f57c00' }}>
          3. Copy the Application (client) ID and Directory (tenant) ID from the Overview page
        </p>
      </div>
    </div>
  );

  const renderPermissionsStep = () => (
    <div style={{ padding: '1rem' }}>
      <h2>Required Permissions</h2>
      <p style={{ marginBottom: '2rem', color: '#666' }}>
        Rolewise.ai requires the following Microsoft Graph permissions to analyze your directory:
      </p>

      <div style={{ marginBottom: '2rem' }}>
        {REQUIRED_PERMISSIONS.map((perm, index) => (
          <div key={index} style={{
            backgroundColor: '#f8f9fa',
            padding: '1rem',
            marginBottom: '1rem',
            borderRadius: '6px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
              <span style={{ color: '#28a745', marginRight: '0.5rem' }}>✓</span>
              <strong style={{ color: '#495057' }}>{perm.permission}</strong>
            </div>
            <p style={{ fontSize: '14px', color: '#6c757d', marginBottom: '0.5rem' }}>
              {perm.description}
            </p>
            <p style={{ fontSize: '12px', color: '#868e96', fontStyle: 'italic' }}>
              Why we need this: {perm.reason}
            </p>
          </div>
        ))}
      </div>

      {authMode === 'app' ? (
        <div style={{ backgroundColor: '#e3f2fd', padding: '1.5rem', borderRadius: '8px', border: '1px solid #2196F3' }}>
          <h4 style={{ color: '#1976d2', marginBottom: '1rem' }}>App Authentication Setup</h4>
          <p style={{ fontSize: '14px', color: '#1976d2', marginBottom: '1rem' }}>
            For app authentication, you need to grant these permissions in your Azure Portal:
          </p>
          <ol style={{ fontSize: '14px', color: '#1976d2', paddingLeft: '1.5rem' }}>
            <li>Go to Azure Portal → Entra ID → App registrations → Your App</li>
            <li>Click "API permissions" → "Add a permission"</li>
            <li>Select "Microsoft Graph" → "Application permissions"</li>
            <li>Add all the permissions listed above</li>
            <li>Click "Grant admin consent" (requires admin privileges)</li>
          </ol>
        </div>
      ) : (
        <div style={{ backgroundColor: '#fff3e0', padding: '1.5rem', borderRadius: '8px', border: '1px solid #ff9800' }}>
          <h4 style={{ color: '#f57c00', marginBottom: '1rem' }}>User Authentication Setup</h4>
          <p style={{ fontSize: '14px', color: '#f57c00', marginBottom: '1rem' }}>
            For user authentication, admin consent will be requested during the login process:
          </p>
          <ul style={{ fontSize: '14px', color: '#f57c00', paddingLeft: '1.5rem' }}>
            <li>You'll be prompted to sign in with an admin account</li>
            <li>Azure will show the permissions being requested</li>
            <li>An admin must approve these permissions for your organization</li>
            <li>Once approved, the integration will be ready to use</li>
          </ul>
        </div>
      )}
    </div>
  );

  const renderTestStep = () => (
    <div style={{ padding: '1rem', textAlign: 'center' }}>
      <h2>Test Connection</h2>
      <p style={{ marginBottom: '2rem', color: '#666' }}>
        Let's test the connection to your Microsoft Entra ID tenant.
      </p>

      <div style={{ backgroundColor: '#f8f9fa', padding: '2rem', borderRadius: '8px', marginBottom: '2rem' }}>
        <h3 style={{ marginBottom: '1rem' }}>Configuration Summary</h3>
        <div style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
          <p><strong>Tenant ID:</strong> {config.tenantId}</p>
          <p><strong>Client ID:</strong> {config.clientId}</p>
          <p><strong>Authentication Mode:</strong> {authMode === 'app' ? 'App (Client Credentials)' : 'User (Interactive)'}</p>
          {authMode === 'app' && (
            <p><strong>Client Secret:</strong> {config.clientSecret ? '••••••••' : 'Not provided'}</p>
          )}
        </div>
      </div>

      {integrationState.error && (
        <div style={{
          backgroundColor: '#ffebee',
          color: '#c62828',
          padding: '1rem',
          borderRadius: '6px',
          marginBottom: '1rem',
          border: '1px solid #ef5350'
        }}>
          <strong>Connection Error:</strong> {integrationState.error}
        </div>
      )}

      {integrationState.isAuthenticated && (
        <div style={{
          backgroundColor: '#e8f5e8',
          color: '#2e7d32',
          padding: '1rem',
          borderRadius: '6px',
          marginBottom: '1rem',
          border: '1px solid #4caf50'
        }}>
          <strong>✓ Connected!</strong> Successfully authenticated to {integrationState.accountInfo?.username || 'Entra ID tenant'}
        </div>
      )}

      <p style={{ fontSize: '14px', color: '#666', marginBottom: '2rem' }}>
        {authMode === 'app'
          ? 'This will verify your client credentials and test access to Microsoft Graph.'
          : 'This will open a popup window for you to sign in and grant permissions.'
        }
      </p>
    </div>
  );

  const renderCompleteStep = () => (
    <div style={{ padding: '2rem', textAlign: 'center' }}>
      <div style={{ fontSize: '48px', marginBottom: '1rem' }}>🎉</div>
      <h2 style={{ color: '#4caf50', marginBottom: '1rem' }}>Integration Complete!</h2>
      <p style={{ fontSize: '16px', color: '#666', marginBottom: '2rem' }}>
        Your Microsoft Entra ID integration has been successfully configured and tested.
      </p>

      <div style={{ backgroundColor: '#e8f5e8', padding: '1.5rem', borderRadius: '8px', marginBottom: '2rem' }}>
        <h3 style={{ color: '#2e7d32', marginBottom: '1rem' }}>What happens next?</h3>
        <ul style={{ textAlign: 'left', color: '#2e7d32', lineHeight: '1.6' }}>
          <li>Your integration settings will be saved securely</li>
          <li>Rolewise.ai can now access your directory data</li>
          <li>You can start analyzing user access patterns and roles</li>
          <li>Data synchronization will begin automatically</li>
        </ul>
      </div>

      <div style={{ backgroundColor: '#f5f5f5', padding: '1rem', borderRadius: '6px' }}>
        <p style={{ fontSize: '14px', color: '#666' }}>
          You can modify these settings anytime from the Integration Setup page.
        </p>
      </div>
    </div>
  );

  return (
    <div style={{
      maxWidth: '800px',
      margin: '0 auto',
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '2rem' }}>
        {renderStepIndicator()}
        
        {currentStep === 'overview' && renderOverviewStep()}
        {currentStep === 'credentials' && renderCredentialsStep()}
        {currentStep === 'permissions' && renderPermissionsStep()}
        {currentStep === 'test' && renderTestStep()}
        {currentStep === 'complete' && renderCompleteStep()}
        
        {/* Navigation buttons */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          marginTop: '2rem',
          paddingTop: '1rem',
          borderTop: '1px solid #e0e0e0'
        }}>
          <button
            onClick={currentStep === 'overview' ? onCancel : handleBack}
            style={{
              padding: '12px 24px',
              backgroundColor: 'transparent',
              border: '1px solid #ccc',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            {currentStep === 'overview' ? 'Cancel' : 'Back'}
          </button>
          
          <button
            onClick={currentStep === 'complete' ? handleComplete : 
                     currentStep === 'test' ? handleTestConnection : handleNext}
            disabled={isLoading}
            style={{
              padding: '12px 24px',
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            {isLoading ? 'Testing...' : 
             currentStep === 'complete' ? 'Finish' :
             currentStep === 'test' ? 'Test Connection' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default IntegrationWizard;
