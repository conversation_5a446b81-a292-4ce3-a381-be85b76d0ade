import React, { useState } from 'react';

interface IntegrationSetupGuideProps {
  onClose: () => void;
}

const IntegrationSetupGuide: React.FC<IntegrationSetupGuideProps> = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'app-registration' | 'permissions' | 'troubleshooting'>('overview');

  const tabStyle = (isActive: boolean) => ({
    padding: '12px 24px',
    backgroundColor: isActive ? '#2196F3' : 'transparent',
    color: isActive ? 'white' : '#666',
    border: 'none',
    borderBottom: isActive ? 'none' : '1px solid #e0e0e0',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: isActive ? 'bold' : 'normal'
  });

  const renderOverview = () => (
    <div>
      <h3>Integration Overview</h3>
      <p style={{ lineHeight: '1.6', marginBottom: '1.5rem' }}>
        The Microsoft Entra ID integration allows Rolewise.ai to analyze your organization's access patterns 
        and provide intelligent role recommendations based on actual usage data.
      </p>

      <div style={{ backgroundColor: '#f8f9fa', padding: '1.5rem', borderRadius: '8px', marginBottom: '1.5rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>What data do we access?</h4>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>Users:</strong> Basic user information and group memberships</li>
          <li><strong>Groups:</strong> Security groups and their members</li>
          <li><strong>Applications:</strong> Registered applications and their configurations</li>
          <li><strong>Directory structure:</strong> Organizational relationships</li>
        </ul>
      </div>

      <div style={{ backgroundColor: '#e3f2fd', padding: '1.5rem', borderRadius: '8px', border: '1px solid #2196F3' }}>
        <h4 style={{ color: '#1976d2', marginBottom: '1rem' }}>Security & Privacy</h4>
        <ul style={{ color: '#1976d2', lineHeight: '1.6' }}>
          <li>All data is encrypted in transit and at rest</li>
          <li>We only request read-only permissions</li>
          <li>No sensitive data like passwords or personal files are accessed</li>
          <li>You can revoke access at any time</li>
        </ul>
      </div>
    </div>
  );

  const renderAppRegistration = () => (
    <div>
      <h3>Creating an App Registration</h3>
      <p style={{ marginBottom: '1.5rem' }}>
        Follow these steps to create an app registration in your Microsoft Entra ID tenant:
      </p>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Step 1: Navigate to App Registrations</h4>
        <ol style={{ lineHeight: '1.8', paddingLeft: '1.5rem' }}>
          <li>Sign in to the <a href="https://portal.azure.com" target="_blank" rel="noopener noreferrer" style={{ color: '#2196F3' }}>Azure Portal</a></li>
          <li>Navigate to <strong>Microsoft Entra ID</strong></li>
          <li>Click on <strong>App registrations</strong> in the left menu</li>
          <li>Click <strong>+ New registration</strong></li>
        </ol>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Step 2: Configure the App Registration</h4>
        <div style={{ backgroundColor: '#f8f9fa', padding: '1rem', borderRadius: '6px', marginBottom: '1rem' }}>
          <p><strong>Name:</strong> Rolewise.ai Integration</p>
          <p><strong>Supported account types:</strong> Accounts in this organizational directory only</p>
          <p><strong>Redirect URI:</strong> Web - <code>{window.location.origin}/integration-callback</code></p>
        </div>
        <p>Click <strong>Register</strong> to create the app.</p>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Step 3: Note Important Values</h4>
        <p>After registration, copy these values from the <strong>Overview</strong> page:</p>
        <ul style={{ lineHeight: '1.6', paddingLeft: '1.5rem' }}>
          <li><strong>Application (client) ID</strong> - You'll need this as the Client ID</li>
          <li><strong>Directory (tenant) ID</strong> - You'll need this as the Tenant ID</li>
        </ul>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Step 4: Create a Client Secret (for App Authentication)</h4>
        <ol style={{ lineHeight: '1.8', paddingLeft: '1.5rem' }}>
          <li>Go to <strong>Certificates & secrets</strong></li>
          <li>Click <strong>+ New client secret</strong></li>
          <li>Add a description (e.g., "Rolewise Integration")</li>
          <li>Choose an expiration period</li>
          <li>Click <strong>Add</strong></li>
          <li><strong>Important:</strong> Copy the secret value immediately - it won't be shown again!</li>
        </ol>
      </div>

      <div style={{ backgroundColor: '#fff3e0', padding: '1.5rem', borderRadius: '8px', border: '1px solid #ff9800' }}>
        <h4 style={{ color: '#f57c00', marginBottom: '1rem' }}>⚠️ Security Best Practices</h4>
        <ul style={{ color: '#f57c00', lineHeight: '1.6' }}>
          <li>Set client secret expiration to a reasonable period (1-2 years)</li>
          <li>Store the secret securely and never share it</li>
          <li>Consider using certificate-based authentication for production</li>
          <li>Regularly rotate secrets before they expire</li>
        </ul>
      </div>
    </div>
  );

  const renderPermissions = () => (
    <div>
      <h3>Configuring API Permissions</h3>
      <p style={{ marginBottom: '1.5rem' }}>
        Configure the required Microsoft Graph permissions for your app registration:
      </p>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Required Permissions</h4>
        <div style={{ backgroundColor: '#f8f9fa', padding: '1rem', borderRadius: '6px', marginBottom: '1rem' }}>
          <ul style={{ lineHeight: '1.6' }}>
            <li><strong>Application.Read.All</strong> - Read all applications</li>
            <li><strong>Group.Read.All</strong> - Read all groups</li>
            <li><strong>User.Read.All</strong> - Read all users</li>
            <li><strong>Directory.Read.All</strong> - Read directory data</li>
          </ul>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Adding Permissions</h4>
        <ol style={{ lineHeight: '1.8', paddingLeft: '1.5rem' }}>
          <li>In your app registration, go to <strong>API permissions</strong></li>
          <li>Click <strong>+ Add a permission</strong></li>
          <li>Select <strong>Microsoft Graph</strong></li>
          <li>Choose <strong>Application permissions</strong> (for app auth) or <strong>Delegated permissions</strong> (for user auth)</li>
          <li>Search for and select each required permission</li>
          <li>Click <strong>Add permissions</strong></li>
        </ol>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ marginBottom: '1rem' }}>Granting Admin Consent</h4>
        <div style={{ backgroundColor: '#ffebee', padding: '1.5rem', borderRadius: '8px', border: '1px solid #f44336', marginBottom: '1rem' }}>
          <p style={{ color: '#c62828', marginBottom: '1rem' }}>
            <strong>Admin consent is required</strong> for these permissions to work.
          </p>
        </div>
        <ol style={{ lineHeight: '1.8', paddingLeft: '1.5rem' }}>
          <li>After adding all permissions, click <strong>Grant admin consent for [Your Organization]</strong></li>
          <li>Confirm the consent when prompted</li>
          <li>Verify that all permissions show "Granted for [Your Organization]" with green checkmarks</li>
        </ol>
      </div>

      <div style={{ backgroundColor: '#e8f5e8', padding: '1.5rem', borderRadius: '8px', border: '1px solid #4caf50' }}>
        <h4 style={{ color: '#2e7d32', marginBottom: '1rem' }}>✓ Permission Types</h4>
        <p style={{ color: '#2e7d32', marginBottom: '1rem' }}>
          <strong>Application permissions:</strong> Used for app authentication (client credentials flow)
        </p>
        <p style={{ color: '#2e7d32' }}>
          <strong>Delegated permissions:</strong> Used for user authentication (interactive flow)
        </p>
      </div>
    </div>
  );

  const renderTroubleshooting = () => (
    <div>
      <h3>Troubleshooting Common Issues</h3>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ color: '#f44336' }}>❌ "Insufficient privileges" error</h4>
        <p style={{ marginBottom: '1rem' }}><strong>Cause:</strong> Admin consent has not been granted for the required permissions.</p>
        <p style={{ marginBottom: '1rem' }}><strong>Solution:</strong></p>
        <ul style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
          <li>Ensure you have Global Administrator or Application Administrator role</li>
          <li>Go to API permissions and click "Grant admin consent"</li>
          <li>Verify all permissions show as "Granted"</li>
        </ul>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ color: '#f44336' }}>❌ "Invalid client" error</h4>
        <p style={{ marginBottom: '1rem' }}><strong>Cause:</strong> Incorrect Client ID or Tenant ID.</p>
        <p style={{ marginBottom: '1rem' }}><strong>Solution:</strong></p>
        <ul style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
          <li>Double-check the Client ID from your app registration</li>
          <li>Verify the Tenant ID from your Entra ID overview page</li>
          <li>Ensure both are valid GUIDs</li>
        </ul>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ color: '#f44336' }}>❌ "Invalid client secret" error</h4>
        <p style={{ marginBottom: '1rem' }}><strong>Cause:</strong> Incorrect or expired client secret.</p>
        <p style={{ marginBottom: '1rem' }}><strong>Solution:</strong></p>
        <ul style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
          <li>Generate a new client secret in your app registration</li>
          <li>Copy the secret value immediately after creation</li>
          <li>Update the integration with the new secret</li>
        </ul>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ color: '#f44336' }}>❌ Popup blocked during user authentication</h4>
        <p style={{ marginBottom: '1rem' }}><strong>Cause:</strong> Browser is blocking the authentication popup.</p>
        <p style={{ marginBottom: '1rem' }}><strong>Solution:</strong></p>
        <ul style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
          <li>Allow popups for this site in your browser</li>
          <li>Try using a different browser</li>
          <li>Disable popup blockers temporarily</li>
        </ul>
      </div>

      <div style={{ backgroundColor: '#e3f2fd', padding: '1.5rem', borderRadius: '8px', border: '1px solid #2196F3' }}>
        <h4 style={{ color: '#1976d2', marginBottom: '1rem' }}>💡 Still having issues?</h4>
        <p style={{ color: '#1976d2' }}>
          Check the browser console for detailed error messages, or contact support with the specific error details.
        </p>
      </div>
    </div>
  );

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '90%',
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
      }}>
        {/* Header */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h2 style={{ margin: 0 }}>Integration Setup Guide</h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div style={{ display: 'flex', borderBottom: '1px solid #e0e0e0' }}>
          <button
            onClick={() => setActiveTab('overview')}
            style={tabStyle(activeTab === 'overview')}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('app-registration')}
            style={tabStyle(activeTab === 'app-registration')}
          >
            App Registration
          </button>
          <button
            onClick={() => setActiveTab('permissions')}
            style={tabStyle(activeTab === 'permissions')}
          >
            Permissions
          </button>
          <button
            onClick={() => setActiveTab('troubleshooting')}
            style={tabStyle(activeTab === 'troubleshooting')}
          >
            Troubleshooting
          </button>
        </div>

        {/* Content */}
        <div style={{
          padding: '2rem',
          maxHeight: '60vh',
          overflowY: 'auto'
        }}>
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'app-registration' && renderAppRegistration()}
          {activeTab === 'permissions' && renderPermissions()}
          {activeTab === 'troubleshooting' && renderTroubleshooting()}
        </div>
      </div>
    </div>
  );
};

export default IntegrationSetupGuide;
