import React, { useEffect, useState } from 'react';
import { Notification, useNotifications } from '../../stores/notificationStore';
import * as styles from './NotificationItem.css';

interface NotificationItemProps {
  notification: Notification;
  index: number;
  total: number;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  index,
  total
}) => {
  const { dismissNotification } = useNotifications();
  const [isVisible, setIsVisible] = useState(false);
  const [progress, setProgress] = useState(100);

  // Animation entrance effect
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 50);
    return () => clearTimeout(timer);
  }, []);

  // Progress bar for auto-hide notifications
  useEffect(() => {
    if (notification.autoHide && notification.duration > 0) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev - (100 / (notification.duration / 100));
          return Math.max(0, newProgress);
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [notification.autoHide, notification.duration]);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      dismissNotification(notification.id);
    }, 300); // Match CSS transition duration
  };

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'loading':
        return '⏳';
      default:
        return 'ℹ️';
    }
  };

  const getProgressColor = () => {
    switch (notification.type) {
      case 'success':
        return '#4caf50';
      case 'error':
        return '#f44336';
      case 'warning':
        return '#ff9800';
      case 'info':
        return '#2196f3';
      case 'loading':
        return '#2196f3';
      default:
        return '#2196f3';
    }
  };

  return (
    <div
      className={`${styles.notification} ${styles[notification.type]} ${
        isVisible ? styles.visible : styles.hidden
      } ${styles[notification.priority]}`}
      style={{
        transform: `translateY(${isVisible ? 0 : 20}px)`,
        opacity: isVisible ? 1 : 0,
        zIndex: 1000 + (total - index)
      }}
    >
      {/* Progress bar for auto-hide notifications */}
      {notification.autoHide && notification.duration > 0 && (
        <div className={styles.progressBar}>
          <div
            className={styles.progressFill}
            style={{
              width: `${progress}%`,
              backgroundColor: getProgressColor()
            }}
          />
        </div>
      )}

      {/* Notification content */}
      <div className={styles.content}>
        <div className={styles.header}>
          <div className={styles.iconTitle}>
            <span className={styles.icon}>{getIcon()}</span>
            <h4 className={styles.title}>{notification.title}</h4>
          </div>
          
          {!notification.persistent && (
            <button
              className={styles.dismissButton}
              onClick={handleDismiss}
              aria-label="Dismiss notification"
            >
              ×
            </button>
          )}
        </div>

        {notification.message && (
          <p className={styles.message}>{notification.message}</p>
        )}

        {/* Progress indicator for loading notifications */}
        {notification.type === 'loading' && notification.metadata?.progress !== undefined && (
          <div className={styles.loadingProgress}>
            <div className={styles.loadingProgressBar}>
              <div
                className={styles.loadingProgressFill}
                style={{ width: `${notification.metadata.progress}%` }}
              />
            </div>
            <span className={styles.loadingProgressText}>
              {notification.metadata.progress}%
            </span>
          </div>
        )}

        {/* Action buttons */}
        {notification.actions && notification.actions.length > 0 && (
          <div className={styles.actions}>
            {notification.actions.map((action, actionIndex) => (
              <button
                key={actionIndex}
                className={`${styles.actionButton} ${styles[action.variant || 'primary']}`}
                onClick={() => {
                  action.action();
                  if (!notification.persistent) {
                    handleDismiss();
                  }
                }}
              >
                {action.label}
              </button>
            ))}
          </div>
        )}

        {/* Context badge */}
        {notification.context && (
          <div className={styles.contextBadge}>
            {notification.context}
          </div>
        )}
      </div>

      {/* Loading spinner for loading notifications */}
      {notification.type === 'loading' && (
        <div className={styles.loadingSpinner}>
          <div className={styles.spinner} />
        </div>
      )}
    </div>
  );
};

export default NotificationItem;
