import { style } from '@vanilla-extract/css';

export const container = style({
  position: 'fixed',
  zIndex: 9999,
  pointerEvents: 'none',
  display: 'flex',
  flexDirection: 'column',
  gap: '12px',
  maxWidth: '400px',
  width: '100%',
  padding: '16px',
});

// Position variants
export const topRight = style({
  top: 0,
  right: 0,
});

export const topLeft = style({
  top: 0,
  left: 0,
});

export const bottomRight = style({
  bottom: 0,
  right: 0,
  flexDirection: 'column-reverse',
});

export const bottomLeft = style({
  bottom: 0,
  left: 0,
  flexDirection: 'column-reverse',
});

export const topCenter = style({
  top: 0,
  left: '50%',
  transform: 'translateX(-50%)',
});

export const bottomCenter = style({
  bottom: 0,
  left: '50%',
  transform: 'translateX(-50%)',
  flexDirection: 'column-reverse',
});
