import React from 'react';
import { useNotifications } from '../../stores/notificationStore';
import NotificationItem from './NotificationItem';
import * as styles from './NotificationContainer.css';

/**
 * Notification Container Component
 * 
 * Renders all active notifications in a fixed position overlay.
 * Handles positioning, stacking, and animation of notifications.
 */

interface NotificationContainerProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  maxVisible?: number;
}

const NotificationContainer: React.FC<NotificationContainerProps> = ({
  position = 'top-right',
  maxVisible = 5
}) => {
  const { notifications } = useNotifications();

  // Filter out dismissed notifications and limit visible count
  const visibleNotifications = notifications
    .filter(notification => !notification.dismissedAt)
    .slice(-maxVisible);

  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div className={`${styles.container} ${styles[position]}`}>
      {visibleNotifications.map((notification, index) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          index={index}
          total={visibleNotifications.length}
        />
      ))}
    </div>
  );
};

export default NotificationContainer;
