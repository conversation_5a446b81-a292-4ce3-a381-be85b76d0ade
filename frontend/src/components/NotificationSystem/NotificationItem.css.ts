import { style, keyframes } from '@vanilla-extract/css';

// Animations
const slideIn = keyframes({
  '0%': { transform: 'translateX(100%)', opacity: 0 },
  '100%': { transform: 'translateX(0)', opacity: 1 }
});

const slideOut = keyframes({
  '0%': { transform: 'translateX(0)', opacity: 1 },
  '100%': { transform: 'translateX(100%)', opacity: 0 }
});

const spin = keyframes({
  '0%': { transform: 'rotate(0deg)' },
  '100%': { transform: 'rotate(360deg)' }
});

// Base notification styles
export const notification = style({
  pointerEvents: 'auto',
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  border: '1px solid #e0e0e0',
  overflow: 'hidden',
  position: 'relative',
  transition: 'all 0.3s ease-in-out',
  minWidth: '320px',
  maxWidth: '400px',
});

// Visibility states
export const visible = style({
  animation: `${slideIn} 0.3s ease-out`,
});

export const hidden = style({
  animation: `${slideOut} 0.3s ease-in`,
});

// Type variants
export const success = style({
  borderLeftColor: '#4caf50',
  borderLeftWidth: '4px',
});

export const error = style({
  borderLeftColor: '#f44336',
  borderLeftWidth: '4px',
});

export const warning = style({
  borderLeftColor: '#ff9800',
  borderLeftWidth: '4px',
});

export const info = style({
  borderLeftColor: '#2196f3',
  borderLeftWidth: '4px',
});

export const loading = style({
  borderLeftColor: '#2196f3',
  borderLeftWidth: '4px',
});

// Priority variants
export const low = style({
  opacity: 0.9,
});

export const medium = style({
  opacity: 1,
});

export const high = style({
  opacity: 1,
  boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
});

export const critical = style({
  opacity: 1,
  boxShadow: '0 8px 24px rgba(244, 67, 54, 0.3)',
  borderColor: '#f44336',
  borderWidth: '2px',
});

// Progress bar
export const progressBar = style({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  height: '3px',
  backgroundColor: '#f0f0f0',
});

export const progressFill = style({
  height: '100%',
  transition: 'width 0.1s linear',
});

// Content
export const content = style({
  padding: '16px',
});

export const header = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  marginBottom: '8px',
});

export const iconTitle = style({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
});

export const icon = style({
  fontSize: '18px',
  flexShrink: 0,
});

export const title = style({
  margin: 0,
  fontSize: '14px',
  fontWeight: '600',
  color: '#333',
  lineHeight: '1.4',
});

export const dismissButton = style({
  background: 'none',
  border: 'none',
  fontSize: '18px',
  color: '#666',
  cursor: 'pointer',
  padding: '0',
  width: '20px',
  height: '20px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '50%',
  transition: 'background-color 0.2s',
  ':hover': {
    backgroundColor: '#f0f0f0',
  },
});

export const message = style({
  margin: 0,
  fontSize: '13px',
  color: '#666',
  lineHeight: '1.5',
  marginBottom: '12px',
});

// Loading progress
export const loadingProgress = style({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  marginTop: '8px',
});

export const loadingProgressBar = style({
  flex: 1,
  height: '6px',
  backgroundColor: '#f0f0f0',
  borderRadius: '3px',
  overflow: 'hidden',
});

export const loadingProgressFill = style({
  height: '100%',
  backgroundColor: '#2196f3',
  transition: 'width 0.3s ease',
});

export const loadingProgressText = style({
  fontSize: '12px',
  color: '#666',
  fontWeight: '500',
  minWidth: '35px',
});

// Actions
export const actions = style({
  display: 'flex',
  gap: '8px',
  marginTop: '12px',
  flexWrap: 'wrap',
});

export const actionButton = style({
  padding: '6px 12px',
  fontSize: '12px',
  fontWeight: '500',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
  transition: 'all 0.2s',
});

export const primary = style({
  backgroundColor: '#2196f3',
  color: 'white',
  ':hover': {
    backgroundColor: '#1976d2',
  },
});

export const secondary = style({
  backgroundColor: '#f5f5f5',
  color: '#333',
  ':hover': {
    backgroundColor: '#e0e0e0',
  },
});

export const danger = style({
  backgroundColor: '#f44336',
  color: 'white',
  ':hover': {
    backgroundColor: '#d32f2f',
  },
});

// Context badge
export const contextBadge = style({
  position: 'absolute',
  top: '8px',
  right: '8px',
  backgroundColor: '#f0f0f0',
  color: '#666',
  fontSize: '10px',
  padding: '2px 6px',
  borderRadius: '10px',
  textTransform: 'uppercase',
  fontWeight: '500',
});

// Loading spinner
export const loadingSpinner = style({
  position: 'absolute',
  top: '16px',
  right: '16px',
});

export const spinner = style({
  width: '16px',
  height: '16px',
  border: '2px solid #f0f0f0',
  borderTop: '2px solid #2196f3',
  borderRadius: '50%',
  animation: `${spin} 1s linear infinite`,
});
