import '@testing-library/jest-dom';
import { vi, afterEach } from 'vitest';

// Set up environment variables for tests
process.env.VITE_SUPABASE_URL = 'https://test-supabase-url.com';
process.env.VITE_SUPABASE_ANON_KEY = 'test-anon-key';

// Mock the config module
vi.mock('../services/config', () => {
  return {
    SUPABASE_URL: 'https://test-supabase-url.com',
    SUPABASE_ANON_KEY: 'test-anon-key',
    BLOCKED_DOMAINS: [
      'gmail.com',
      'yahoo.com',
      'hotmail.com',
      'outlook.com',
      'aol.com',
      'icloud.com',
    ],
  };
});

// Mock the Supabase client
vi.mock('../services/supabaseClient', () => {
  const mockSupabase = {
    auth: {
      getSession: vi.fn().mockResolvedValue({ data: { session: null }, error: null }),
      signInWithPassword: vi.fn(),
      signOut: vi.fn(),
      signUp: vi.fn(),
      resetPasswordForEmail: vi.fn(),
      onAuthStateChange: vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } }),
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      maybeSingle: vi.fn(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      onConflict: vi.fn().mockReturnThis(),
    }),
  };

  return {
    supabase: mockSupabase,
  };
});

// Mock the createClient function from Supabase
vi.mock('@supabase/supabase-js', () => {
  return {
    createClient: vi.fn(() => ({
      auth: {
        getSession: vi.fn().mockResolvedValue({ data: { session: null }, error: null }),
        signInWithPassword: vi.fn(),
        signOut: vi.fn(),
      },
      from: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        upsert: vi.fn().mockReturnThis(),
        onConflict: vi.fn().mockReturnThis(),
      }),
    })),
  };
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock import.meta.env
vi.stubGlobal('import.meta', {
  env: {
    VITE_SUPABASE_URL: 'https://test-supabase-url.com',
    VITE_SUPABASE_ANON_KEY: 'test-anon-key',
  }
});

// Clean up after each test
afterEach(() => {
  vi.clearAllMocks();
});
