import React, { Suspense, useEffect } from 'react';
import { Routes, Route, Outlet } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Sidebar from './components/Sidebar';
import Navbar from './components/Navbar';
import { RequireAuth } from './components/Auth0Auth';
import NotificationContainer from './components/NotificationSystem/NotificationContainer';
import { useAppState } from './stores/appStateStore';

import * as styles from './App.css';

// Lazy load pages
const Login = React.lazy(() => import('./pages/Login'));
const SignUp = React.lazy(() => import('./pages/SignUp'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Settings = React.lazy(() => import('./pages/Settings'));
const Users = React.lazy(() => import('./pages/Users'));
const Roles = React.lazy(() => import('./pages/Roles'));
const Permissions = React.lazy(() => import('./pages/Permissions'));
const AccessInsights = React.lazy(() => import('./pages/AccessInsights'));
const IntegrationSetup = React.lazy(() => import('./pages/IntegrationSetup'));
const IntegrationCallback = React.lazy(() => import('./pages/IntegrationCallback'));
const ProcessingStatusDashboard = React.lazy(() => import('./pages/ProcessingStatusDashboard'));
const NotFound = React.lazy(() => import('./pages/NotFound'));

// Loading component with better styling
const LoadingFallback = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '50vh',
    fontSize: '16px',
    color: '#666'
  }}>
    Loading...
  </div>
);

/**
 * Main application layout component
 * This contains the Navbar, Sidebar, and main content area
 * Only rendered for authenticated users
 */
const AppLayout = () => {
  const { setOnlineStatus } = useAppState();

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => setOnlineStatus(true);
    const handleOffline = () => setOnlineStatus(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial status
    setOnlineStatus(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [setOnlineStatus]);

  return (
    <div className={styles.appContainer}>
      <Navbar />
      <div className={styles.mainLayout}>
        <Sidebar />
        <main className={styles.mainContent}>
          <Outlet />
        </main>
      </div>
      {/* Global notification system */}
      <NotificationContainer position="top-right" maxVisible={5} />
    </div>
  );
};

/**
 * Protected layout wrapper
 * This is the key fix - clean separation of auth and layout concerns
 */
const ProtectedAppLayout = () => {
  return (
    <RequireAuth>
      <AppLayout />
    </RequireAuth>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Public routes - no authentication required */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<SignUp />} />

          {/* Integration callback route - public but specific to integration auth */}
          <Route path="/integration-callback" element={<IntegrationCallback />} />

          {/* Protected routes - all require authentication and include app layout */}
          <Route element={<ProtectedAppLayout />}>
            <Route path="/" element={<Dashboard />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/users" element={<Users />} />
            <Route path="/roles" element={<Roles />} />
            <Route path="/permissions" element={<Permissions />} />
            <Route path="/access-insights" element={<AccessInsights />} />
            <Route path="/integration-setup" element={<IntegrationSetup />} />
            <Route path="/processing-status" element={<ProcessingStatusDashboard />} />
          </Route>

          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export default App;