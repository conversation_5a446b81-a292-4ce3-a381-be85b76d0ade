import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuthStatus } from '../../types/auth';

// Mock Auth0
vi.mock('@auth0/auth0-react', () => ({
  useAuth0: vi.fn(),
}));

// Import the store after mocking dependencies
import { useAuthStore, initializeUserProfile, clearAuthState, getDatabaseUserId } from '../authStore';

describe('Auth Store', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset the store state
    useAuthStore.setState({
      user: null,
      status: AuthStatus.LOADING,
      userDetails: null,
      tenantId: null,
      permissions: [],
      accessToken: null,
    });
    
    // Clear localStorage
    localStorage.clear();
  });

  describe('Store State Management', () => {
    it('should initialize with correct default state', () => {
      const state = useAuthStore.getState();
      
      expect(state.user).toBeNull();
      expect(state.status).toBe(AuthStatus.LOADING);
      expect(state.userDetails).toBeNull();
      expect(state.tenantId).toBeNull();
      expect(state.permissions).toEqual([]);
      expect(state.accessToken).toBeNull();
    });

    it('should update user state', () => {
      const mockUser = { 
        sub: 'auth0|123', 
        email: '<EMAIL>', 
        name: 'Test User' 
      };
      
      useAuthStore.getState().setUser(mockUser);
      
      expect(useAuthStore.getState().user).toEqual(mockUser);
    });

    it('should update auth status', () => {
      useAuthStore.getState().setStatus(AuthStatus.AUTHENTICATED);
      
      expect(useAuthStore.getState().status).toBe(AuthStatus.AUTHENTICATED);
    });

    it('should update access token', () => {
      const token = 'test-access-token';
      
      useAuthStore.getState().setAccessToken(token);
      
      expect(useAuthStore.getState().accessToken).toBe(token);
    });

    it('should update user details', () => {
      const mockUserDetails = {
        id: 'user-id',
        name: 'Test User',
        email: '<EMAIL>',
        tenant_id: 'tenant-123',
        role_id: 'admin',
        created_at: '2023-01-01T00:00:00.000Z'
      };
      
      useAuthStore.getState().setUserDetails(mockUserDetails);
      
      expect(useAuthStore.getState().userDetails).toEqual(mockUserDetails);
    });

    it('should update tenant ID', () => {
      const tenantId = 'tenant-123';
      
      useAuthStore.getState().setTenantId(tenantId);
      
      expect(useAuthStore.getState().tenantId).toBe(tenantId);
    });

    it('should update permissions', () => {
      const permissions = ['read:users', 'write:users'];
      
      useAuthStore.getState().setPermissions(permissions);
      
      expect(useAuthStore.getState().permissions).toEqual(permissions);
    });

    it('should clear auth state', () => {
      // Set initial state
      useAuthStore.setState({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        status: AuthStatus.AUTHENTICATED,
        userDetails: { 
          id: 'user-id', 
          name: 'Test User', 
          email: '<EMAIL>', 
          tenant_id: 'tenant-id',
          role_id: 'admin',
          created_at: '2023-01-01T00:00:00.000Z'
        },
        tenantId: 'tenant-id',
        permissions: ['read:users'],
        accessToken: 'test-token',
      });
      
      useAuthStore.getState().clearAuth();
      
      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.status).toBe(AuthStatus.UNAUTHENTICATED);
      expect(state.userDetails).toBeNull();
      expect(state.tenantId).toBeNull();
      expect(state.permissions).toEqual([]);
      expect(state.accessToken).toBeNull();
    });
  });

  describe('Helper Functions', () => {
    it('should clear auth state and localStorage', () => {
      // Set some initial state
      useAuthStore.setState({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        status: AuthStatus.AUTHENTICATED,
      });
      
      clearAuthState();
      
      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.status).toBe(AuthStatus.UNAUTHENTICATED);
    });

    it('should get database user ID from internal user ID', () => {
      useAuthStore.setState({
        user: { 
          sub: 'auth0|123', 
          email: '<EMAIL>',
          internalUserId: 'internal-123'
        },
      });
      
      const userId = getDatabaseUserId();
      expect(userId).toBe('internal-123');
    });

    it('should fallback to Auth0 user ID when no internal ID', () => {
      useAuthStore.setState({
        user: { 
          sub: 'auth0|123', 
          email: '<EMAIL>'
        },
      });
      
      const userId = getDatabaseUserId();
      expect(userId).toBe('auth0|123');
    });

    it('should return null when no user', () => {
      const userId = getDatabaseUserId();
      expect(userId).toBeNull();
    });
  });
});
