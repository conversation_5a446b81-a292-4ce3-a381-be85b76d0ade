import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { useNotificationStore } from './notificationStore';

/**
 * Enhanced Application State Store
 * 
 * Manages global application state with improved error handling,
 * loading states, and user feedback integration.
 */

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface OperationState {
  id: string;
  name: string;
  status: LoadingState;
  progress?: number;
  error?: string;
  startedAt: number;
  completedAt?: number;
  context?: string;
  metadata?: Record<string, any>;
}

export interface AppError {
  id: string;
  code: string;
  message: string;
  details?: string;
  context?: string;
  timestamp: number;
  acknowledged: boolean;
  recoverable: boolean;
  retryCount: number;
  maxRetries: number;
}

export interface AppStateStore {
  // Loading states
  operations: Record<string, OperationState>;
  globalLoading: boolean;
  
  // Error management
  errors: AppError[];
  lastError: AppError | null;
  
  // User preferences
  preferences: {
    theme: 'light' | 'dark' | 'system';
    notifications: {
      enabled: boolean;
      position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
      autoHide: boolean;
      duration: number;
    };
    debug: boolean;
  };
  
  // Network status
  isOnline: boolean;
  lastSyncAt: number | null;
  
  // Actions
  startOperation: (name: string, context?: string, metadata?: Record<string, any>) => string;
  updateOperation: (id: string, updates: Partial<OperationState>) => void;
  completeOperation: (id: string, success?: boolean, error?: string) => void;
  cancelOperation: (id: string) => void;
  clearOperations: (context?: string) => void;
  
  addError: (error: Omit<AppError, 'id' | 'timestamp' | 'acknowledged' | 'retryCount'>) => string;
  acknowledgeError: (id: string) => void;
  retryError: (id: string, retryFn?: () => Promise<void>) => Promise<void>;
  clearErrors: (context?: string) => void;
  
  updatePreferences: (preferences: Partial<AppStateStore['preferences']>) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  updateLastSync: () => void;
  
  // Utility methods
  isOperationActive: (context?: string) => boolean;
  getOperationsByContext: (context: string) => OperationState[];
  getErrorsByContext: (context: string) => AppError[];
  hasUnacknowledgedErrors: () => boolean;
}

// Generate unique operation ID
function generateOperationId(): string {
  return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Generate unique error ID
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export const useAppStateStore = create<AppStateStore>()(
  persist(
    (set, get) => ({
      // Initial state
      operations: {},
      globalLoading: false,
      errors: [],
      lastError: null,
      isOnline: navigator.onLine,
      lastSyncAt: null,
      
      preferences: {
        theme: 'system',
        notifications: {
          enabled: true,
          position: 'top-right',
          autoHide: true,
          duration: 5000
        },
        debug: false
      },

      // Operation management
      startOperation: (name, context, metadata) => {
        const id = generateOperationId();
        const operation: OperationState = {
          id,
          name,
          status: 'loading',
          startedAt: Date.now(),
          context,
          metadata
        };

        set((state) => ({
          operations: { ...state.operations, [id]: operation },
          globalLoading: Object.keys(state.operations).length === 0 // First operation
        }));

        // Show loading notification if enabled
        const { preferences } = get();
        if (preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          notificationStore.showLoading(
            name,
            'Operation in progress...',
            { context, metadata: { operationId: id } }
          );
        }

        return id;
      },

      updateOperation: (id, updates) => {
        set((state) => {
          const operation = state.operations[id];
          if (!operation) return state;

          const updatedOperation = { ...operation, ...updates };
          
          return {
            operations: { ...state.operations, [id]: updatedOperation }
          };
        });

        // Update notification if it exists
        const operation = get().operations[id];
        if (operation && get().preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          const notifications = notificationStore.notifications.filter(
            n => n.metadata?.operationId === id
          );
          
          if (notifications.length > 0) {
            const notification = notifications[0];
            if (updates.progress !== undefined) {
              notificationStore.updateProgress(notification.id, operation.name, updates.progress);
            }
          }
        }
      },

      completeOperation: (id, success = true, error) => {
        const operation = get().operations[id];
        if (!operation) return;

        const completedOperation: OperationState = {
          ...operation,
          status: success ? 'success' : 'error',
          completedAt: Date.now(),
          error
        };

        set((state) => {
          const newOperations = { ...state.operations, [id]: completedOperation };
          const activeOperations = Object.values(newOperations).filter(op => op.status === 'loading');
          
          return {
            operations: newOperations,
            globalLoading: activeOperations.length > 0
          };
        });

        // Update notification
        if (get().preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          const notifications = notificationStore.notifications.filter(
            n => n.metadata?.operationId === id
          );
          
          if (notifications.length > 0) {
            const notification = notifications[0];
            if (success) {
              notificationStore.completeProgress(notification.id, `${operation.name} completed successfully`);
            } else {
              notificationStore.failProgress(
                notification.id, 
                error || `${operation.name} failed`,
                error ? [{
                  label: 'Retry',
                  action: () => {
                    // Retry logic would be implemented by the calling component
                    console.log('Retry operation:', id);
                  }
                }] : undefined
              );
            }
          }
        }

        // Clean up completed operations after a delay
        setTimeout(() => {
          set((state) => {
            const { [id]: removed, ...remainingOperations } = state.operations;
            return { operations: remainingOperations };
          });
        }, 30000); // Keep for 30 seconds
      },

      cancelOperation: (id) => {
        set((state) => {
          const { [id]: removed, ...remainingOperations } = state.operations;
          const activeOperations = Object.values(remainingOperations).filter(op => op.status === 'loading');
          
          return {
            operations: remainingOperations,
            globalLoading: activeOperations.length > 0
          };
        });

        // Remove notification
        if (get().preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          const notifications = notificationStore.notifications.filter(
            n => n.metadata?.operationId === id
          );
          
          notifications.forEach(notification => {
            notificationStore.removeNotification(notification.id);
          });
        }
      },

      clearOperations: (context) => {
        set((state) => {
          const filteredOperations = context
            ? Object.fromEntries(
                Object.entries(state.operations).filter(([_, op]) => op.context !== context)
              )
            : {};
          
          return {
            operations: filteredOperations,
            globalLoading: Object.values(filteredOperations).some(op => op.status === 'loading')
          };
        });
      },

      // Error management
      addError: (errorData) => {
        const id = generateErrorId();
        const error: AppError = {
          id,
          timestamp: Date.now(),
          acknowledged: false,
          retryCount: 0,
          maxRetries: errorData.maxRetries || 3,
          ...errorData
        };

        set((state) => ({
          errors: [...state.errors, error],
          lastError: error
        }));

        // Show error notification
        if (get().preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          notificationStore.showError(
            error.message,
            error.details,
            {
              context: error.context,
              persistent: true,
              actions: error.recoverable ? [{
                label: 'Retry',
                action: () => get().retryError(id),
                variant: 'primary'
              }, {
                label: 'Dismiss',
                action: () => get().acknowledgeError(id),
                variant: 'secondary'
              }] : [{
                label: 'Dismiss',
                action: () => get().acknowledgeError(id),
                variant: 'secondary'
              }]
            }
          );
        }

        return id;
      },

      acknowledgeError: (id) => {
        set((state) => ({
          errors: state.errors.map(error =>
            error.id === id ? { ...error, acknowledged: true } : error
          )
        }));
      },

      retryError: async (id, retryFn) => {
        const error = get().errors.find(e => e.id === id);
        if (!error || error.retryCount >= error.maxRetries) return;

        set((state) => ({
          errors: state.errors.map(e =>
            e.id === id ? { ...e, retryCount: e.retryCount + 1 } : e
          )
        }));

        if (retryFn) {
          try {
            await retryFn();
            // If successful, remove the error
            set((state) => ({
              errors: state.errors.filter(e => e.id !== id)
            }));
          } catch (retryError) {
            // Update error with new details
            set((state) => ({
              errors: state.errors.map(e =>
                e.id === id ? { 
                  ...e, 
                  details: retryError instanceof Error ? retryError.message : String(retryError)
                } : e
              )
            }));
          }
        }
      },

      clearErrors: (context) => {
        set((state) => ({
          errors: context
            ? state.errors.filter(error => error.context !== context)
            : []
        }));
      },

      // Preferences
      updatePreferences: (newPreferences) => {
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences }
        }));
      },

      // Network status
      setOnlineStatus: (isOnline) => {
        set({ isOnline });
        
        if (isOnline && get().preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          notificationStore.showSuccess('Connection restored', 'You are back online');
        } else if (!isOnline && get().preferences.notifications.enabled) {
          const notificationStore = useNotificationStore.getState();
          notificationStore.showWarning('Connection lost', 'You are currently offline');
        }
      },

      updateLastSync: () => {
        set({ lastSyncAt: Date.now() });
      },

      // Utility methods
      isOperationActive: (context) => {
        const operations = Object.values(get().operations);
        return context
          ? operations.some(op => op.context === context && op.status === 'loading')
          : operations.some(op => op.status === 'loading');
      },

      getOperationsByContext: (context) => {
        return Object.values(get().operations).filter(op => op.context === context);
      },

      getErrorsByContext: (context) => {
        return get().errors.filter(error => error.context === context);
      },

      hasUnacknowledgedErrors: () => {
        return get().errors.some(error => !error.acknowledged);
      }
    }),
    {
      name: 'app-state-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        preferences: state.preferences,
        // Don't persist operations and errors - they should be fresh on app start
      })
    }
  )
);

// React hook for app state management
export function useAppState() {
  const store = useAppStateStore();
  
  return {
    // State
    operations: store.operations,
    globalLoading: store.globalLoading,
    errors: store.errors,
    lastError: store.lastError,
    preferences: store.preferences,
    isOnline: store.isOnline,
    lastSyncAt: store.lastSyncAt,
    
    // Actions
    startOperation: store.startOperation,
    updateOperation: store.updateOperation,
    completeOperation: store.completeOperation,
    cancelOperation: store.cancelOperation,
    clearOperations: store.clearOperations,
    
    addError: store.addError,
    acknowledgeError: store.acknowledgeError,
    retryError: store.retryError,
    clearErrors: store.clearErrors,
    
    updatePreferences: store.updatePreferences,
    setOnlineStatus: store.setOnlineStatus,
    updateLastSync: store.updateLastSync,
    
    // Utilities
    isOperationActive: store.isOperationActive,
    getOperationsByContext: store.getOperationsByContext,
    getErrorsByContext: store.getErrorsByContext,
    hasUnacknowledgedErrors: store.hasUnacknowledgedErrors
  };
}
