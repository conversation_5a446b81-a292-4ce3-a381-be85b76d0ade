import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

/**
 * Unified Notification System
 * 
 * Provides consistent user feedback across the entire application:
 * - Toast notifications for temporary messages
 * - Persistent notifications for important updates
 * - Error notifications with recovery actions
 * - Success confirmations
 * - Progress notifications for long-running operations
 */

export type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical';

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
}

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  priority: NotificationPriority;
  persistent: boolean;
  autoHide: boolean;
  duration: number; // in milliseconds
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  createdAt: number;
  dismissedAt?: number;
  context?: string; // e.g., 'integration', 'auth', 'data-processing'
}

export interface NotificationState {
  notifications: Notification[];
  maxNotifications: number;
  defaultDuration: number;
  
  // Actions
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => string;
  removeNotification: (id: string) => void;
  dismissNotification: (id: string) => void;
  clearNotifications: (context?: string) => void;
  clearAllNotifications: () => void;
  
  // Convenience methods
  showSuccess: (title: string, message?: string, options?: Partial<Notification>) => string;
  showError: (title: string, message?: string, options?: Partial<Notification>) => string;
  showWarning: (title: string, message?: string, options?: Partial<Notification>) => string;
  showInfo: (title: string, message?: string, options?: Partial<Notification>) => string;
  showLoading: (title: string, message?: string, options?: Partial<Notification>) => string;
  
  // Progress notifications
  showProgress: (title: string, message?: string, context?: string) => string;
  updateProgress: (id: string, message: string, progress?: number) => void;
  completeProgress: (id: string, successMessage?: string) => void;
  failProgress: (id: string, errorMessage?: string, actions?: NotificationAction[]) => void;
}

// Generate unique notification ID
function generateNotificationId(): string {
  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Default notification settings
const DEFAULT_SETTINGS = {
  maxNotifications: 5,
  defaultDuration: 5000, // 5 seconds
  durations: {
    success: 4000,
    error: 8000,
    warning: 6000,
    info: 5000,
    loading: 0 // Don't auto-hide loading notifications
  }
};

export const useNotificationStore = create<NotificationState>()(
  persist(
    (set, get) => ({
      notifications: [],
      maxNotifications: DEFAULT_SETTINGS.maxNotifications,
      defaultDuration: DEFAULT_SETTINGS.defaultDuration,

      addNotification: (notificationData) => {
        const id = generateNotificationId();
        const notification: Notification = {
          id,
          createdAt: Date.now(),
          priority: 'medium',
          persistent: false,
          autoHide: true,
          duration: DEFAULT_SETTINGS.durations[notificationData.type] || DEFAULT_SETTINGS.defaultDuration,
          ...notificationData
        };

        set((state) => {
          let newNotifications = [...state.notifications, notification];
          
          // Remove oldest notifications if we exceed the maximum
          if (newNotifications.length > state.maxNotifications) {
            newNotifications = newNotifications.slice(-state.maxNotifications);
          }
          
          return { notifications: newNotifications };
        });

        // Auto-hide notification if configured
        if (notification.autoHide && notification.duration > 0) {
          setTimeout(() => {
            get().removeNotification(id);
          }, notification.duration);
        }

        return id;
      },

      removeNotification: (id) => {
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        }));
      },

      dismissNotification: (id) => {
        set((state) => ({
          notifications: state.notifications.map(n => 
            n.id === id ? { ...n, dismissedAt: Date.now() } : n
          )
        }));
        
        // Remove after a short delay to allow for animation
        setTimeout(() => {
          get().removeNotification(id);
        }, 300);
      },

      clearNotifications: (context) => {
        set((state) => ({
          notifications: context 
            ? state.notifications.filter(n => n.context !== context)
            : []
        }));
      },

      clearAllNotifications: () => {
        set({ notifications: [] });
      },

      // Convenience methods
      showSuccess: (title, message, options = {}) => {
        return get().addNotification({
          type: 'success',
          title,
          message,
          priority: 'medium',
          persistent: false,
          autoHide: true,
          duration: DEFAULT_SETTINGS.durations.success,
          ...options
        });
      },

      showError: (title, message, options = {}) => {
        return get().addNotification({
          type: 'error',
          title,
          message,
          priority: 'high',
          persistent: true,
          autoHide: false,
          duration: DEFAULT_SETTINGS.durations.error,
          ...options
        });
      },

      showWarning: (title, message, options = {}) => {
        return get().addNotification({
          type: 'warning',
          title,
          message,
          priority: 'medium',
          persistent: false,
          autoHide: true,
          duration: DEFAULT_SETTINGS.durations.warning,
          ...options
        });
      },

      showInfo: (title, message, options = {}) => {
        return get().addNotification({
          type: 'info',
          title,
          message,
          priority: 'low',
          persistent: false,
          autoHide: true,
          duration: DEFAULT_SETTINGS.durations.info,
          ...options
        });
      },

      showLoading: (title, message, options = {}) => {
        return get().addNotification({
          type: 'loading',
          title,
          message,
          priority: 'medium',
          persistent: true,
          autoHide: false,
          duration: 0,
          ...options
        });
      },

      // Progress notifications
      showProgress: (title, message, context) => {
        return get().addNotification({
          type: 'loading',
          title,
          message,
          priority: 'medium',
          persistent: true,
          autoHide: false,
          duration: 0,
          context,
          metadata: { progress: 0 }
        });
      },

      updateProgress: (id, message, progress) => {
        set((state) => ({
          notifications: state.notifications.map(n => 
            n.id === id 
              ? { 
                  ...n, 
                  message, 
                  metadata: { ...n.metadata, progress } 
                }
              : n
          )
        }));
      },

      completeProgress: (id, successMessage) => {
        set((state) => ({
          notifications: state.notifications.map(n => 
            n.id === id 
              ? { 
                  ...n, 
                  type: 'success' as NotificationType,
                  message: successMessage || 'Completed successfully',
                  autoHide: true,
                  duration: DEFAULT_SETTINGS.durations.success
                }
              : n
          )
        }));

        // Auto-hide after success
        setTimeout(() => {
          get().removeNotification(id);
        }, DEFAULT_SETTINGS.durations.success);
      },

      failProgress: (id, errorMessage, actions) => {
        set((state) => ({
          notifications: state.notifications.map(n => 
            n.id === id 
              ? { 
                  ...n, 
                  type: 'error' as NotificationType,
                  message: errorMessage || 'Operation failed',
                  autoHide: false,
                  persistent: true,
                  actions
                }
              : n
          )
        }));
      }
    }),
    {
      name: 'notification-storage',
      storage: createJSONStorage(() => sessionStorage), // Use session storage for notifications
      partialize: (state) => ({
        // Only persist critical notifications across sessions
        notifications: state.notifications.filter(n => n.priority === 'critical' && n.persistent)
      })
    }
  )
);

/**
 * React hook for notification management
 */
export function useNotifications() {
  const store = useNotificationStore();
  
  return {
    notifications: store.notifications,
    addNotification: store.addNotification,
    removeNotification: store.removeNotification,
    dismissNotification: store.dismissNotification,
    clearNotifications: store.clearNotifications,
    clearAllNotifications: store.clearAllNotifications,
    
    // Convenience methods
    showSuccess: store.showSuccess,
    showError: store.showError,
    showWarning: store.showWarning,
    showInfo: store.showInfo,
    showLoading: store.showLoading,
    
    // Progress methods
    showProgress: store.showProgress,
    updateProgress: store.updateProgress,
    completeProgress: store.completeProgress,
    failProgress: store.failProgress,
    
    // Utility methods
    hasNotifications: store.notifications.length > 0,
    hasErrors: store.notifications.some(n => n.type === 'error'),
    hasWarnings: store.notifications.some(n => n.type === 'warning'),
    getNotificationsByContext: (context: string) => 
      store.notifications.filter(n => n.context === context),
    getNotificationsByType: (type: NotificationType) => 
      store.notifications.filter(n => n.type === type)
  };
}
