import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { AccountInfo } from '@azure/msal-browser';
import { 
  getAccessToken, 
  clearIntegrationAuth, 
  getIntegrationAuthStatus,
  testEntraIdConnection 
} from '../services/api/entra-id-auth';

// Types for integration authentication state
export interface IntegrationConfig {
  tenantId: string;
  clientId: string;
  clientSecret?: string;
}

export interface IntegrationAuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  accountInfo: AccountInfo | null;
  accessToken: string | null;
  config: IntegrationConfig | null;
}

export interface IntegrationAuthActions {
  authenticateIntegration: (config: IntegrationConfig) => Promise<boolean>;
  testConnection: (config: IntegrationConfig) => Promise<boolean>;
  clearAuthentication: () => Promise<void>;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

// Action types for reducer
type IntegrationAuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_AUTHENTICATED'; payload: { accountInfo: AccountInfo; accessToken: string; config: IntegrationConfig } }
  | { type: 'SET_UNAUTHENTICATED' }
  | { type: 'SET_CONFIG'; payload: IntegrationConfig };

// Initial state
const initialState: IntegrationAuthState = {
  isAuthenticated: false,
  isLoading: false,
  error: null,
  accountInfo: null,
  accessToken: null,
  config: null,
};

// Reducer function
function integrationAuthReducer(state: IntegrationAuthState, action: IntegrationAuthAction): IntegrationAuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload, error: null };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_AUTHENTICATED':
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        accountInfo: action.payload.accountInfo,
        accessToken: action.payload.accessToken,
        config: action.payload.config,
      };
    
    case 'SET_UNAUTHENTICATED':
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        accountInfo: null,
        accessToken: null,
        config: null,
      };
    
    case 'SET_CONFIG':
      return { ...state, config: action.payload };
    
    default:
      return state;
  }
}

// Context creation
const EntraIdIntegrationContext = createContext<{
  state: IntegrationAuthState;
  actions: IntegrationAuthActions;
} | null>(null);

// Provider component
export const EntraIdIntegrationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(integrationAuthReducer, initialState);

  // Action implementations
  const authenticateIntegration = useCallback(async (config: IntegrationConfig): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_CONFIG', payload: config });

    try {
      console.log(`[Integration Context] Authenticating to tenant ${config.tenantId}`);
      
      // Check if already authenticated
      const authStatus = getIntegrationAuthStatus(config.tenantId, config.clientId);
      if (authStatus.isAuthenticated && authStatus.accountInfo) {
        console.log(`[Integration Context] Already authenticated to tenant ${config.tenantId}`);
        
        // Try to get a fresh token to verify the authentication is still valid
        try {
          const accessToken = await getAccessToken(config.tenantId, config.clientId);
          dispatch({
            type: 'SET_AUTHENTICATED',
            payload: {
              accountInfo: authStatus.accountInfo,
              accessToken,
              config,
            },
          });
          return true;
        } catch (tokenError) {
          console.log(`[Integration Context] Existing token invalid, re-authenticating`);
          // Continue with fresh authentication
        }
      }

      // Perform authentication
      const accessToken = await getAccessToken(config.tenantId, config.clientId);
      
      // Get updated auth status
      const updatedAuthStatus = getIntegrationAuthStatus(config.tenantId, config.clientId);
      
      if (updatedAuthStatus.isAuthenticated && updatedAuthStatus.accountInfo) {
        dispatch({
          type: 'SET_AUTHENTICATED',
          payload: {
            accountInfo: updatedAuthStatus.accountInfo,
            accessToken,
            config,
          },
        });
        console.log(`[Integration Context] Successfully authenticated to tenant ${config.tenantId}`);
        return true;
      } else {
        throw new Error('Authentication completed but no account found');
      }
    } catch (error: any) {
      console.error(`[Integration Context] Authentication failed for tenant ${config.tenantId}:`, error);
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Authentication failed' });
      return false;
    }
  }, []);

  const testConnection = useCallback(async (config: IntegrationConfig): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      console.log(`[Integration Context] Testing connection to tenant ${config.tenantId}`);
      const result = await testEntraIdConnection(config.tenantId, config.clientId, config.clientSecret);
      
      if (!result) {
        dispatch({ type: 'SET_ERROR', payload: 'Connection test failed. Please check your credentials.' });
      } else {
        dispatch({ type: 'SET_ERROR', payload: null });
      }
      
      dispatch({ type: 'SET_LOADING', payload: false });
      return result;
    } catch (error: any) {
      console.error(`[Integration Context] Connection test failed for tenant ${config.tenantId}:`, error);
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Connection test failed' });
      return false;
    }
  }, []);

  const clearAuthentication = useCallback(async (): Promise<void> => {
    if (state.config) {
      console.log(`[Integration Context] Clearing authentication for tenant ${state.config.tenantId}`);
      await clearIntegrationAuth(state.config.tenantId, state.config.clientId);
    }
    dispatch({ type: 'SET_UNAUTHENTICATED' });
  }, [state.config]);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.config) {
        clearIntegrationAuth(state.config.tenantId, state.config.clientId).catch(console.error);
      }
    };
  }, [state.config]);

  const actions: IntegrationAuthActions = {
    authenticateIntegration,
    testConnection,
    clearAuthentication,
    setError,
    setLoading,
  };

  return (
    <EntraIdIntegrationContext.Provider value={{ state, actions }}>
      {children}
    </EntraIdIntegrationContext.Provider>
  );
};

// Hook to use the integration context
export const useEntraIdIntegration = () => {
  const context = useContext(EntraIdIntegrationContext);
  if (!context) {
    throw new Error('useEntraIdIntegration must be used within an EntraIdIntegrationProvider');
  }
  return context;
};

// Hook to get just the state (for read-only access)
export const useEntraIdIntegrationState = () => {
  const { state } = useEntraIdIntegration();
  return state;
};

// Hook to get just the actions (for components that only need to trigger actions)
export const useEntraIdIntegrationActions = () => {
  const { actions } = useEntraIdIntegration();
  return actions;
};
