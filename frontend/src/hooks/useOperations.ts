import { useCallback } from 'react';
import { useAppState } from '../stores/appStateStore';
import { useNotifications } from '../stores/notificationStore';

/**
 * Enhanced Operations Hook
 * 
 * Provides a unified interface for managing long-running operations
 * with integrated progress tracking, error handling, and user feedback.
 */

export interface OperationOptions {
  showNotifications?: boolean;
  showProgress?: boolean;
  context?: string;
  metadata?: Record<string, any>;
  onSuccess?: (result?: any) => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
}

export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  operationId: string;
}

export function useOperations() {
  const appState = useAppState();
  const notifications = useNotifications();

  /**
   * Executes an async operation with full state management
   */
  const executeOperation = useCallback(async <T = any>(
    name: string,
    operation: (updateProgress?: (progress: number, message?: string) => void) => Promise<T>,
    options: OperationOptions = {}
  ): Promise<OperationResult<T>> => {
    const {
      showNotifications = true,
      showProgress = true,
      context,
      metadata,
      onSuccess,
      onError,
      onProgress
    } = options;

    // Start operation tracking
    const operationId = appState.startOperation(name, context, metadata);
    
    let progressNotificationId: string | undefined;
    
    if (showNotifications && showProgress) {
      progressNotificationId = notifications.showProgress(
        name,
        'Starting operation...',
        context
      );
    }

    // Progress update function
    const updateProgress = (progress: number, message?: string) => {
      appState.updateOperation(operationId, { progress });
      
      if (progressNotificationId) {
        notifications.updateProgress(progressNotificationId, message || name, progress);
      }
      
      onProgress?.(progress);
    };

    try {
      // Execute the operation
      const result = await operation(updateProgress);
      
      // Complete operation successfully
      appState.completeOperation(operationId, true);
      
      if (progressNotificationId) {
        notifications.completeProgress(progressNotificationId, `${name} completed successfully`);
      } else if (showNotifications) {
        notifications.showSuccess(
          `${name} completed`,
          'Operation completed successfully',
          { context, autoHide: true, duration: 3000 }
        );
      }
      
      onSuccess?.(result);
      
      return {
        success: true,
        data: result,
        operationId
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Complete operation with error
      appState.completeOperation(operationId, false, errorMessage);
      
      // Add structured error
      appState.addError({
        code: 'OPERATION_FAILED',
        message: `${name} failed`,
        details: errorMessage,
        context,
        recoverable: true,
        maxRetries: 2
      });
      
      if (progressNotificationId) {
        notifications.failProgress(
          progressNotificationId,
          `${name} failed: ${errorMessage}`,
          [{
            label: 'Retry',
            action: () => executeOperation(name, operation, options),
            variant: 'primary'
          }]
        );
      } else if (showNotifications) {
        notifications.showError(
          `${name} failed`,
          errorMessage,
          {
            context,
            actions: [{
              label: 'Retry',
              action: () => executeOperation(name, operation, options),
              variant: 'primary'
            }]
          }
        );
      }
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
      
      return {
        success: false,
        error: errorMessage,
        operationId
      };
    }
  }, [appState, notifications]);

  /**
   * Executes multiple operations in sequence
   */
  const executeSequentialOperations = useCallback(async (
    operations: Array<{
      name: string;
      operation: (updateProgress?: (progress: number, message?: string) => void) => Promise<any>;
      options?: OperationOptions;
    }>
  ): Promise<OperationResult[]> => {
    const results: OperationResult[] = [];
    
    for (let i = 0; i < operations.length; i++) {
      const { name, operation, options = {} } = operations[i];
      
      const result = await executeOperation(
        name,
        (updateProgress) => {
          // Adjust progress to account for multiple operations
          const adjustedUpdateProgress = (progress: number, message?: string) => {
            const overallProgress = ((i / operations.length) * 100) + (progress / operations.length);
            updateProgress?.(overallProgress, message);
          };
          
          return operation(adjustedUpdateProgress);
        },
        {
          ...options,
          context: options.context || 'batch-operations'
        }
      );
      
      results.push(result);
      
      // Stop if any operation fails (unless configured otherwise)
      if (!result.success && !options.continueOnError) {
        break;
      }
    }
    
    return results;
  }, [executeOperation]);

  /**
   * Executes multiple operations in parallel
   */
  const executeParallelOperations = useCallback(async (
    operations: Array<{
      name: string;
      operation: (updateProgress?: (progress: number, message?: string) => void) => Promise<any>;
      options?: OperationOptions;
    }>
  ): Promise<OperationResult[]> => {
    const promises = operations.map(({ name, operation, options = {} }) =>
      executeOperation(name, operation, {
        ...options,
        context: options.context || 'parallel-operations'
      })
    );
    
    return Promise.all(promises);
  }, [executeOperation]);

  /**
   * Cancels an active operation
   */
  const cancelOperation = useCallback((operationId: string) => {
    appState.cancelOperation(operationId);
    
    // Find and remove related notifications
    const relatedNotifications = notifications.notifications.filter(
      n => n.metadata?.operationId === operationId
    );
    
    relatedNotifications.forEach(notification => {
      notifications.removeNotification(notification.id);
    });
  }, [appState, notifications]);

  /**
   * Gets the status of an operation
   */
  const getOperationStatus = useCallback((operationId: string) => {
    return appState.operations[operationId];
  }, [appState.operations]);

  /**
   * Checks if any operations are active in a specific context
   */
  const hasActiveOperations = useCallback((context?: string) => {
    return appState.isOperationActive(context);
  }, [appState]);

  /**
   * Gets all operations for a specific context
   */
  const getOperationsByContext = useCallback((context: string) => {
    return appState.getOperationsByContext(context);
  }, [appState]);

  /**
   * Clears completed operations for a context
   */
  const clearOperations = useCallback((context?: string) => {
    appState.clearOperations(context);
  }, [appState]);

  return {
    // Core operation methods
    executeOperation,
    executeSequentialOperations,
    executeParallelOperations,
    
    // Operation management
    cancelOperation,
    getOperationStatus,
    hasActiveOperations,
    getOperationsByContext,
    clearOperations,
    
    // State access
    operations: appState.operations,
    globalLoading: appState.globalLoading,
    
    // Utility methods
    isLoading: (context?: string) => hasActiveOperations(context),
    getActiveOperationsCount: (context?: string) => {
      const ops = context 
        ? getOperationsByContext(context)
        : Object.values(appState.operations);
      return ops.filter(op => op.status === 'loading').length;
    }
  };
}

/**
 * Hook for managing a specific operation context
 */
export function useOperationContext(context: string) {
  const operations = useOperations();
  
  return {
    executeOperation: (
      name: string,
      operation: (updateProgress?: (progress: number, message?: string) => void) => Promise<any>,
      options: Omit<OperationOptions, 'context'> = {}
    ) => operations.executeOperation(name, operation, { ...options, context }),
    
    hasActiveOperations: () => operations.hasActiveOperations(context),
    getOperations: () => operations.getOperationsByContext(context),
    clearOperations: () => operations.clearOperations(context),
    isLoading: operations.isLoading(context),
    
    // Context-specific state
    operations: operations.getOperationsByContext(context),
    activeCount: operations.getActiveOperationsCount(context)
  };
}
