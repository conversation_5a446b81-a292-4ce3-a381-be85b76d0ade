import { useAuthStore } from '../../stores/authStore';

/**
 * Unified API Client for consistent authentication across all API calls
 * 
 * This client ensures that:
 * 1. All API calls use proper Auth0 authentication
 * 2. Tokens are retrieved consistently
 * 3. Error handling is standardized
 * 4. Request/response patterns are unified
 */

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
  status: number;
}

export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
  requireAuth?: boolean;
  timeout?: number;
}

class ApiClient {
  private baseUrl: string;
  private defaultTimeout: number = 30000; // 30 seconds

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  /**
   * Gets the current Auth0 access token
   */
  private async getAccessToken(): Promise<string> {
    const { accessToken, isAuthenticated } = useAuthStore.getState();
    
    if (!isAuthenticated) {
      throw new Error('User is not authenticated');
    }
    
    if (!accessToken) {
      throw new Error('No access token available');
    }
    
    return accessToken;
  }

  /**
   * Makes an authenticated API request
   */
  async request<T = any>(
    endpoint: string, 
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      body,
      headers = {},
      requireAuth = true,
      timeout = this.defaultTimeout
    } = options;

    const url = `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    
    // Prepare headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    };

    // Add authentication if required
    if (requireAuth) {
      try {
        const accessToken = await this.getAccessToken();
        requestHeaders['Authorization'] = `Bearer ${accessToken}`;
      } catch (error) {
        console.error('[API Client] Authentication error:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Authentication failed',
          status: 401
        };
      }
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers: requestHeaders,
      signal: AbortSignal.timeout(timeout)
    };

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    try {
      console.log(`[API Client] ${method} ${url}`, {
        hasAuth: !!requestHeaders['Authorization'],
        bodySize: requestOptions.body ? requestOptions.body.length : 0
      });

      const response = await fetch(url, requestOptions);
      
      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      const apiResponse: ApiResponse<T> = {
        success: response.ok,
        status: response.status,
        data: response.ok ? responseData : undefined,
        error: response.ok ? undefined : (responseData?.error || responseData || `HTTP ${response.status}`)
      };

      if (!response.ok) {
        console.error(`[API Client] Request failed:`, {
          url,
          status: response.status,
          error: apiResponse.error
        });
      }

      return apiResponse;
    } catch (error) {
      console.error(`[API Client] Network error:`, error);
      
      if (error instanceof Error && error.name === 'TimeoutError') {
        return {
          success: false,
          error: 'Request timeout',
          status: 408
        };
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        status: 0
      };
    }
  }

  /**
   * Convenience methods for common HTTP verbs
   */
  async get<T = any>(endpoint: string, options: Omit<ApiRequestOptions, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(endpoint: string, body?: any, options: Omit<ApiRequestOptions, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  async put<T = any>(endpoint: string, body?: any, options: Omit<ApiRequestOptions, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  async patch<T = any>(endpoint: string, body?: any, options: Omit<ApiRequestOptions, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body });
  }

  async delete<T = any>(endpoint: string, options: Omit<ApiRequestOptions, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
}

// Create API client instances
export const supabaseApiClient = new ApiClient(import.meta.env.VITE_SUPABASE_URL);
export const edgeFunctionClient = new ApiClient(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1`);

/**
 * Specialized client for Supabase Edge Functions
 */
export class EdgeFunctionClient extends ApiClient {
  constructor() {
    super(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1`);
  }

  /**
   * Calls a Supabase Edge Function with proper authentication
   */
  async invokeFunction<T = any>(
    functionName: string, 
    payload?: any, 
    options: Omit<ApiRequestOptions, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.post<T>(`/${functionName}`, payload, options);
  }

  /**
   * Tests Entra ID connection via edge function
   */
  async testEntraConnection(tenantId: string, clientId: string, clientSecret?: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.invokeFunction('get-entra-token', {
      tenant_id: tenantId,
      client_id: clientId,
      client_secret: clientSecret,
      test_only: true
    });
  }

  /**
   * Fetches Entra ID data via edge function
   */
  async fetchEntraData(tenantId: string, forceRefresh?: boolean): Promise<ApiResponse<any>> {
    return this.invokeFunction('fetch-entra-data', {
      tenantId,
      forceRefresh
    });
  }

  /**
   * Processes tenant data via edge function
   */
  async processTenantData(tenantId: string, data: any): Promise<ApiResponse<any>> {
    return this.invokeFunction('process-tenant-data', {
      tenantId,
      ...data
    });
  }

  /**
   * Infers roles via edge function
   */
  async inferRoles(tenantId: string, options?: any): Promise<ApiResponse<any>> {
    return this.invokeFunction('infer-roles', {
      tenantId,
      ...options
    });
  }

  /**
   * Manages webhooks via edge function
   */
  async manageWebhooks(action: string, payload?: any): Promise<ApiResponse<any>> {
    return this.invokeFunction('manage-webhooks', {
      action,
      ...payload
    });
  }
}

// Export singleton instance
export const edgeFunctions = new EdgeFunctionClient();

/**
 * Hook for using the API client in React components
 */
export function useApiClient() {
  return {
    supabaseApi: supabaseApiClient,
    edgeFunctions: edgeFunctions,
    // Convenience method to check if user is authenticated
    isAuthenticated: () => useAuthStore.getState().isAuthenticated
  };
}

/**
 * Error handling utilities
 */
export function isApiError(response: ApiResponse): response is ApiResponse & { error: string } {
  return !response.success && !!response.error;
}

export function getApiErrorMessage(response: ApiResponse, defaultMessage: string = 'An error occurred'): string {
  if (isApiError(response)) {
    return response.error;
  }
  return defaultMessage;
}

/**
 * Retry utility for failed requests
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<ApiResponse<T>> {
  let lastResponse: ApiResponse<T>;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    lastResponse = await apiCall();
    
    if (lastResponse.success || lastResponse.status === 401 || lastResponse.status === 403) {
      // Don't retry on success or auth errors
      return lastResponse;
    }
    
    if (attempt < maxRetries) {
      console.log(`[API Client] Retry attempt ${attempt}/${maxRetries} after ${delayMs}ms`);
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
    }
  }
  
  return lastResponse!;
}
