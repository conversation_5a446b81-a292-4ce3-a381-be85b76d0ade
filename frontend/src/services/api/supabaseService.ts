import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useAuthStore } from '../../stores/authStore';
import { edgeFunctions, ApiResponse } from './apiClient';
import {
  SupabaseApplication,
  SupabaseGroup,
  SupabaseUserGroupMembership,
  SupabaseRoleApplicationMapping
} from '../../types/supabase';

/**
 * Enhanced Supabase Service with consistent Auth0 authentication
 * 
 * This service provides:
 * 1. Authenticated Supabase client with Auth0 tokens
 * 2. Consistent error handling
 * 3. Unified API patterns
 * 4. Edge function integration
 */

class SupabaseService {
  private client: SupabaseClient | null = null;
  private readonly supabaseUrl: string;
  private readonly supabaseAnonKey: string;

  constructor() {
    this.supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    this.supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!this.supabaseUrl || !this.supabaseAnonKey) {
      throw new Error('Supabase URL and Key must be provided in environment variables');
    }
  }

  /**
   * Gets an authenticated Supabase client
   */
  private getClient(): SupabaseClient {
    if (!this.client) {
      const { accessToken, isAuthenticated } = useAuthStore.getState();
      
      // Create client with Auth0 token if available
      const clientOptions: any = {
        auth: {
          autoRefreshToken: false, // We handle tokens via Auth0
          persistSession: false,   // Auth0 handles session persistence
          detectSessionInUrl: false
        }
      };

      // Add Auth0 token to global headers if authenticated
      if (isAuthenticated && accessToken) {
        clientOptions.global = {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        };
      }

      this.client = createClient(this.supabaseUrl, this.supabaseAnonKey, clientOptions);
    }

    return this.client;
  }

  /**
   * Refreshes the client with new authentication
   */
  refreshClient(): void {
    this.client = null;
  }

  /**
   * Database Operations
   */

  // Applications
  async storeApplications(tenantId: string, applications: SupabaseApplication[]): Promise<void> {
    const client = this.getClient();
    
    for (const app of applications) {
      const embeddingString = Array.isArray(app.embedding)
        ? JSON.stringify(app.embedding)
        : app.embedding;

      const { error } = await client
        .from('applications')
        .upsert({
          tenant_id: tenantId,
          name: app.name,
          description: app.description || null,
          embedding: embeddingString,
        }, { onConflict: 'name' });

      if (error) {
        throw new Error(`Failed to store application ${app.name}: ${error.message}`);
      }
    }
  }

  async getApplicationsForTenant(tenantId: string): Promise<SupabaseApplication[]> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('applications')
      .select('*')
      .eq('tenant_id', tenantId);

    if (error) {
      throw new Error(`Failed to fetch applications: ${error.message}`);
    }

    return data || [];
  }

  // Groups
  async storeGroups(tenantId: string, groups: SupabaseGroup[]): Promise<void> {
    const client = this.getClient();
    
    for (const group of groups) {
      const { error } = await client
        .from('security_groups')
        .upsert({
          tenant_id: tenantId,
          display_name: group.display_name,
          description: group.description || null,
        }, { onConflict: 'display_name' });

      if (error) {
        throw new Error(`Failed to store group ${group.display_name}: ${error.message}`);
      }
    }
  }

  async getGroupsForTenant(tenantId: string): Promise<SupabaseGroup[]> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('security_groups')
      .select('*')
      .eq('tenant_id', tenantId);

    if (error) {
      throw new Error(`Failed to fetch groups: ${error.message}`);
    }

    return data || [];
  }

  // Users
  async getUsersForTenant(tenantId: string): Promise<{ id: string }[]> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('profiles')
      .select('id')
      .eq('tenant_id', tenantId);

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`);
    }

    return data || [];
  }

  async getUserGroups(tenantId: string, userId: string): Promise<SupabaseGroup[]> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('user_group_memberships')
      .select('security_groups!inner(display_name, description)')
      .eq('tenant_id', tenantId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to fetch user groups: ${error.message}`);
    }

    return data?.map((item: any) => item.security_groups) || [];
  }

  // Roles
  async getRolesForTenant(tenantId: string): Promise<any[]> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('roles')
      .select('id, name, description')
      .eq('tenant_id', tenantId);

    if (error) {
      throw new Error(`Failed to fetch roles: ${error.message}`);
    }

    return data || [];
  }

  async createRole(tenantId: string, name: string, description: string): Promise<string> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('roles')
      .insert({ name, tenant_id: tenantId, description })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create role: ${error.message}`);
    }

    return data.id;
  }

  async assignRoleToUser(userId: string, roleId: string): Promise<void> {
    const client = this.getClient();
    
    const { error } = await client
      .from('profiles')
      .update({ role_id: roleId })
      .eq('id', userId);

    if (error) {
      throw new Error(`Failed to assign role: ${error.message}`);
    }
  }

  // Integrations
  async getIntegration(tenantId: string): Promise<any> {
    const client = this.getClient();
    
    const { data, error } = await client
      .from('integrations')
      .select('*')
      .eq('tenant_id', tenantId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(`Failed to fetch integration: ${error.message}`);
    }

    return data;
  }

  async saveIntegration(tenantId: string, integrationData: any): Promise<void> {
    const client = this.getClient();
    
    const { error } = await client
      .from('integrations')
      .upsert({
        tenant_id: tenantId,
        ...integrationData
      });

    if (error) {
      throw new Error(`Failed to save integration: ${error.message}`);
    }
  }

  /**
   * Edge Function Operations
   */

  async fetchEntraData(tenantId: string, forceRefresh?: boolean): Promise<ApiResponse<any>> {
    return edgeFunctions.fetchEntraData(tenantId, forceRefresh);
  }

  async processTenantData(tenantId: string, data: any): Promise<ApiResponse<any>> {
    return edgeFunctions.processTenantData(tenantId, data);
  }

  async inferRoles(tenantId: string, options?: any): Promise<ApiResponse<any>> {
    return edgeFunctions.inferRoles(tenantId, options);
  }

  async testEntraConnection(tenantId: string, clientId: string, clientSecret?: string): Promise<ApiResponse<{ success: boolean }>> {
    return edgeFunctions.testEntraConnection(tenantId, clientId, clientSecret);
  }

  /**
   * Utility Methods
   */

  async checkAuthentication(): Promise<boolean> {
    const { isAuthenticated } = useAuthStore.getState();
    return isAuthenticated;
  }

  async getTenantId(): Promise<string | null> {
    const { user } = useAuthStore.getState();
    return user?.user_metadata?.tenant_id || null;
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();

/**
 * React hook for using the Supabase service
 */
export function useSupabaseService() {
  return {
    service: supabaseService,
    refreshAuth: () => supabaseService.refreshClient(),
    isAuthenticated: () => supabaseService.checkAuthentication()
  };
}

/**
 * Legacy compatibility - gradually migrate existing code to use supabaseService
 */
export const {
  storeApplications,
  storeGroups,
  getUsersForTenant,
  getUserGroups,
  getRolesForTenant,
  createRole,
  assignRoleToUser
} = {
  storeApplications: (tenantId: string, applications: SupabaseApplication[]) => 
    supabaseService.storeApplications(tenantId, applications),
  storeGroups: (tenantId: string, groups: SupabaseGroup[]) => 
    supabaseService.storeGroups(tenantId, groups),
  getUsersForTenant: (tenantId: string) => 
    supabaseService.getUsersForTenant(tenantId),
  getUserGroups: (tenantId: string, userId: string) => 
    supabaseService.getUserGroups(tenantId, userId),
  getRolesForTenant: (tenantId: string) => 
    supabaseService.getRolesForTenant(tenantId),
  createRole: (tenantId: string, name: string, description: string) => 
    supabaseService.createRole(tenantId, name, description),
  assignRoleToUser: (userId: string, roleId: string) => 
    supabaseService.assignRoleToUser(userId, roleId)
};
