import { useAuthStore } from '../../stores/authStore';

/**
 * Authentication Middleware for API Clients
 * 
 * This middleware ensures consistent authentication handling across all API calls:
 * 1. Token validation and refresh
 * 2. Automatic retry on auth failures
 * 3. Consistent error handling
 * 4. Request/response logging
 */

export interface AuthMiddlewareOptions {
  requireAuth?: boolean;
  retryOnAuthFailure?: boolean;
  maxRetries?: number;
  logRequests?: boolean;
}

export interface AuthenticatedRequest {
  url: string;
  options: RequestInit;
  metadata: {
    requireAuth: boolean;
    attempt: number;
    startTime: number;
  };
}

export interface AuthMiddlewareResult {
  success: boolean;
  request?: AuthenticatedRequest;
  error?: string;
  shouldRetry?: boolean;
}

class AuthMiddleware {
  private defaultOptions: Required<AuthMiddlewareOptions> = {
    requireAuth: true,
    retryOnAuthFailure: true,
    maxRetries: 2,
    logRequests: true
  };

  /**
   * Prepares a request with authentication
   */
  async prepareRequest(
    url: string, 
    options: RequestInit = {}, 
    middlewareOptions: AuthMiddlewareOptions = {}
  ): Promise<AuthMiddlewareResult> {
    const config = { ...this.defaultOptions, ...middlewareOptions };
    const startTime = Date.now();

    try {
      // Clone options to avoid mutation
      const requestOptions: RequestInit = {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      };

      // Add authentication if required
      if (config.requireAuth) {
        const authResult = await this.addAuthentication(requestOptions);
        if (!authResult.success) {
          return {
            success: false,
            error: authResult.error,
            shouldRetry: false
          };
        }
      }

      const authenticatedRequest: AuthenticatedRequest = {
        url,
        options: requestOptions,
        metadata: {
          requireAuth: config.requireAuth,
          attempt: 1,
          startTime
        }
      };

      if (config.logRequests) {
        this.logRequest(authenticatedRequest);
      }

      return {
        success: true,
        request: authenticatedRequest
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to prepare request',
        shouldRetry: false
      };
    }
  }

  /**
   * Handles response and determines if retry is needed
   */
  async handleResponse(
    response: Response,
    request: AuthenticatedRequest,
    middlewareOptions: AuthMiddlewareOptions = {}
  ): Promise<AuthMiddlewareResult> {
    const config = { ...this.defaultOptions, ...middlewareOptions };
    const duration = Date.now() - request.metadata.startTime;

    if (config.logRequests) {
      this.logResponse(response, request, duration);
    }

    // Handle authentication failures
    if (response.status === 401 || response.status === 403) {
      if (config.retryOnAuthFailure && request.metadata.attempt < config.maxRetries) {
        console.log(`[Auth Middleware] Auth failure, attempting retry ${request.metadata.attempt + 1}/${config.maxRetries}`);
        
        // Try to refresh authentication
        const refreshResult = await this.refreshAuthentication();
        if (refreshResult.success) {
          // Update request with new auth and increment attempt
          const updatedRequest: AuthenticatedRequest = {
            ...request,
            metadata: {
              ...request.metadata,
              attempt: request.metadata.attempt + 1,
              startTime: Date.now()
            }
          };

          // Re-add authentication with fresh token
          if (request.metadata.requireAuth) {
            await this.addAuthentication(updatedRequest.options);
          }

          return {
            success: true,
            request: updatedRequest,
            shouldRetry: true
          };
        }
      }

      return {
        success: false,
        error: response.status === 401 ? 'Authentication required' : 'Access denied',
        shouldRetry: false
      };
    }

    // Handle other errors
    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        shouldRetry: response.status >= 500 && request.metadata.attempt < config.maxRetries
      };
    }

    return { success: true };
  }

  /**
   * Adds authentication headers to request
   */
  private async addAuthentication(options: RequestInit): Promise<{ success: boolean; error?: string }> {
    try {
      const { accessToken, isAuthenticated } = useAuthStore.getState();

      if (!isAuthenticated) {
        return {
          success: false,
          error: 'User is not authenticated'
        };
      }

      if (!accessToken) {
        return {
          success: false,
          error: 'No access token available'
        };
      }

      // Add Authorization header
      options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${accessToken}`
      };

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Attempts to refresh authentication
   */
  private async refreshAuthentication(): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real implementation, this would trigger Auth0 token refresh
      // For now, we'll just check if the user is still authenticated
      const { isAuthenticated, accessToken } = useAuthStore.getState();
      
      if (isAuthenticated && accessToken) {
        return { success: true };
      }

      return {
        success: false,
        error: 'Unable to refresh authentication'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication refresh failed'
      };
    }
  }

  /**
   * Logs request details
   */
  private logRequest(request: AuthenticatedRequest): void {
    const { url, options, metadata } = request;
    const method = options.method || 'GET';
    const hasAuth = !!(options.headers as any)?.['Authorization'];
    
    console.log(`[Auth Middleware] ${method} ${url}`, {
      attempt: metadata.attempt,
      requireAuth: metadata.requireAuth,
      hasAuth,
      bodySize: options.body ? (options.body as string).length : 0
    });
  }

  /**
   * Logs response details
   */
  private logResponse(response: Response, request: AuthenticatedRequest, duration: number): void {
    const { url, metadata } = request;
    const method = request.options.method || 'GET';
    
    const logLevel = response.ok ? 'log' : 'error';
    console[logLevel](`[Auth Middleware] ${method} ${url} - ${response.status}`, {
      attempt: metadata.attempt,
      duration: `${duration}ms`,
      ok: response.ok,
      status: response.status,
      statusText: response.statusText
    });
  }

  /**
   * Utility method to check if user is authenticated
   */
  isAuthenticated(): boolean {
    return useAuthStore.getState().isAuthenticated;
  }

  /**
   * Utility method to get current user info
   */
  getCurrentUser() {
    return useAuthStore.getState().user;
  }

  /**
   * Utility method to get current tenant ID
   */
  getCurrentTenantId(): string | null {
    const user = this.getCurrentUser();
    return user?.user_metadata?.tenant_id || null;
  }
}

// Export singleton instance
export const authMiddleware = new AuthMiddleware();

/**
 * Higher-order function to wrap fetch with authentication middleware
 */
export async function authenticatedFetch(
  url: string,
  options: RequestInit = {},
  middlewareOptions: AuthMiddlewareOptions = {}
): Promise<Response> {
  let currentRequest: AuthenticatedRequest | undefined;
  
  // Prepare initial request
  const prepareResult = await authMiddleware.prepareRequest(url, options, middlewareOptions);
  if (!prepareResult.success || !prepareResult.request) {
    throw new Error(prepareResult.error || 'Failed to prepare request');
  }
  
  currentRequest = prepareResult.request;
  
  // Execute request with retry logic
  while (true) {
    try {
      const response = await fetch(currentRequest.url, currentRequest.options);
      
      // Handle response
      const handleResult = await authMiddleware.handleResponse(response, currentRequest, middlewareOptions);
      
      if (handleResult.success && !handleResult.shouldRetry) {
        return response;
      }
      
      if (handleResult.shouldRetry && handleResult.request) {
        currentRequest = handleResult.request;
        continue; // Retry with updated request
      }
      
      // If we get here, the request failed and shouldn't be retried
      throw new Error(handleResult.error || 'Request failed');
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to server');
      }
      throw error;
    }
  }
}

/**
 * React hook for using authentication middleware
 */
export function useAuthMiddleware() {
  return {
    middleware: authMiddleware,
    authenticatedFetch,
    isAuthenticated: () => authMiddleware.isAuthenticated(),
    getCurrentUser: () => authMiddleware.getCurrentUser(),
    getCurrentTenantId: () => authMiddleware.getCurrentTenantId()
  };
}
