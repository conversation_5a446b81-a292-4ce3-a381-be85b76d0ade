import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  storeApplications, 
  storeGroups, 
  getUsersForTenant,
  getUserGroups,
  getRolesForTenant,
  createRole,
  assignRoleToUser
} from '../supabase';

// Mock the supabase client
vi.mock('../../supabaseClient', () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    upsert: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
    maybeSingle: vi.fn(),
    onConflict: vi.fn().mockReturnThis(),
  },
}));

// Mock environment variables
vi.mock('../../config', () => ({
  SUPABASE_URL: 'https://mock-supabase-url.com',
  SUPABASE_ANON_KEY: 'mock-anon-key',
}));

describe('Supabase API Service', () => {
  const mockSupabase = {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    upsert: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
    maybeSingle: vi.fn(),
    onConflict: vi.fn().mockReturnThis(),
  };

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Reset the mock implementation
    const { supabase } = await import('../../supabaseClient');
    Object.keys(mockSupabase).forEach(key => {
      if (typeof supabase[key] === 'function') {
        supabase[key] = vi.fn().mockReturnThis();
      }
    });
    
    // Set up the from method to return the mock
    supabase.from = vi.fn().mockReturnValue(mockSupabase);
  });

  describe('storeApplications', () => {
    it('should store applications in Supabase', async () => {
      const { supabase } = await import('../../supabaseClient');
      
      // Set up the mock response
      mockSupabase.upsert.mockReturnValue({ error: null });
      
      // Call the function
      const tenantId = 'test-tenant';
      const applications = [
        { name: 'App 1', description: 'Description 1', embedding: [0.1, 0.2, 0.3] },
        { name: 'App 2', description: 'Description 2', embedding: JSON.stringify([0.4, 0.5, 0.6]) },
      ];
      
      await storeApplications(tenantId, applications);
      
      // Verify that from was called with the correct table
      expect(supabase.from).toHaveBeenCalledWith('applications');
      
      // Verify that upsert was called twice (once for each application)
      expect(mockSupabase.upsert).toHaveBeenCalledTimes(2);
      
      // Verify the first upsert call
      expect(mockSupabase.upsert).toHaveBeenCalledWith(
        {
          tenant_id: tenantId,
          name: 'App 1',
          description: 'Description 1',
          embedding: JSON.stringify([0.1, 0.2, 0.3]),
        },
        { onConflict: 'name' }
      );
      
      // Verify the second upsert call
      expect(mockSupabase.upsert).toHaveBeenCalledWith(
        {
          tenant_id: tenantId,
          name: 'App 2',
          description: 'Description 2',
          embedding: JSON.stringify([0.4, 0.5, 0.6]),
        },
        { onConflict: 'name' }
      );
    });

    it('should handle errors when storing applications', async () => {
      const { supabase } = await import('../../supabaseClient');
      
      // Set up the mock response with an error
      mockSupabase.upsert.mockReturnValue({ error: { message: 'Database error' } });
      
      // Call the function
      const tenantId = 'test-tenant';
      const applications = [{ name: 'App 1', description: 'Description 1', embedding: [0.1, 0.2, 0.3] }];
      
      // Expect the function to throw an error
      await expect(storeApplications(tenantId, applications)).rejects.toThrow(
        'Failed to store application App 1: Database error'
      );
      
      // Verify that from was called with the correct table
      expect(supabase.from).toHaveBeenCalledWith('applications');
      
      // Verify that upsert was called
      expect(mockSupabase.upsert).toHaveBeenCalledTimes(1);
    });
  });

  describe('getUsersForTenant', () => {
    it('should fetch users for a tenant', async () => {
      const { supabase } = await import('../../supabaseClient');
      
      // Set up the mock response
      mockSupabase.select.mockReturnThis();
      mockSupabase.eq.mockReturnThis();
      mockSupabase.eq.mockReturnValue({ 
        data: [{ id: 'user1' }, { id: 'user2' }], 
        error: null 
      });
      
      // Call the function
      const tenantId = 'test-tenant';
      const result = await getUsersForTenant(tenantId);
      
      // Verify that from was called with the correct table
      expect(supabase.from).toHaveBeenCalledWith('profiles');
      
      // Verify that select was called with the correct fields
      expect(mockSupabase.select).toHaveBeenCalledWith('id');
      
      // Verify that eq was called with the correct parameters
      expect(mockSupabase.eq).toHaveBeenCalledWith('tenant_id', tenantId);
      
      // Verify the result
      expect(result).toEqual([{ id: 'user1' }, { id: 'user2' }]);
    });

    it('should handle errors when fetching users', async () => {
      const { supabase } = await import('../../supabaseClient');
      
      // Set up the mock response with an error
      mockSupabase.select.mockReturnThis();
      mockSupabase.eq.mockReturnThis();
      mockSupabase.eq.mockReturnValue({ 
        data: null, 
        error: { message: 'Database error' } 
      });
      
      // Call the function
      const tenantId = 'test-tenant';
      
      // Expect the function to throw an error
      await expect(getUsersForTenant(tenantId)).rejects.toThrow(
        'Failed to fetch users: Database error'
      );
      
      // Verify that from was called with the correct table
      expect(supabase.from).toHaveBeenCalledWith('profiles');
    });
  });

  // Add more tests for other functions as needed
});
