import { describe, it, expect } from 'vitest';

describe('Config', () => {
  it('should export Supabase URL and key from environment variables', async () => {
    // Import the module (mocked by setup.ts)
    const { SUPABASE_URL, SUPABASE_ANON_KEY } = await import('../config');

    // Verify the values (these come from the mock in setup.ts)
    expect(SUPABASE_URL).toBe('https://test-supabase-url.com');
    expect(SUPABASE_ANON_KEY).toBe('test-anon-key');
  });

  it('should export BLOCKED_DOMAINS array', async () => {
    // Import the module (mocked by setup.ts)
    const { BLOCKED_DOMAINS } = await import('../config');

    // Verify the array (comes from the mock in setup.ts)
    expect(BLOCKED_DOMAINS).toBeInstanceOf(Array);
    expect(BLOCKED_DOMAINS).toContain('gmail.com');
    expect(BLOCKED_DOMAINS).toContain('yahoo.com');
    expect(BLOCKED_DOMAINS).toContain('hotmail.com');
    expect(BLOCKED_DOMAINS).toContain('outlook.com');
    expect(BLOCKED_DOMAINS).toContain('aol.com');
    expect(BLOCKED_DOMAINS).toContain('icloud.com');
  });
});
