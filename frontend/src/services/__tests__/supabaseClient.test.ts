import { describe, it, expect } from 'vitest';

describe('Supabase Client', () => {
  it('should create a Supabase client', async () => {
    // Import the module (mocked by setup.ts)
    const { supabase } = await import('../supabaseClient');

    // Verify that the client was created and has the expected interface
    expect(supabase).toBeDefined();
    expect(supabase.auth).toBeDefined();
    expect(supabase.from).toBeDefined();
    expect(typeof supabase.auth.getSession).toBe('function');
    expect(typeof supabase.auth.signInWithPassword).toBe('function');
    expect(typeof supabase.auth.signOut).toBe('function');
  });

  it('should have auth methods available', async () => {
    // Import the module (mocked by setup.ts)
    const { supabase } = await import('../supabaseClient');

    // Test that auth methods exist and are mockable
    expect(supabase.auth.getSession).toBeDefined();
    expect(supabase.auth.signInWithPassword).toBeDefined();
    expect(supabase.auth.signOut).toBeDefined();
    expect(supabase.auth.signUp).toBeDefined();
    expect(supabase.auth.resetPasswordForEmail).toBeDefined();
    expect(supabase.auth.onAuthStateChange).toBeDefined();
  });

  it('should have database methods available', async () => {
    // Import the module (mocked by setup.ts)
    const { supabase } = await import('../supabaseClient');

    // Test that database methods exist
    expect(supabase.from).toBeDefined();
    expect(typeof supabase.from).toBe('function');
    
    // Test the chain methods
    const table = supabase.from('test');
    expect(table.select).toBeDefined();
    expect(table.insert).toBeDefined();
    expect(table.update).toBeDefined();
    expect(table.upsert).toBeDefined();
  });
});
