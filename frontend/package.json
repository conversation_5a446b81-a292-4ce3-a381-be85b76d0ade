{"name": "rolewise", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@auth0/auth0-react": "^2.4.0", "@azure/msal-browser": "^4.13.1", "@supabase/supabase-js": "^2.50.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vanilla-extract/css": "^1.17.4", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "reactflow": "^11.11.4", "vitest": "^3.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@types/node": "^24.0.3", "@vanilla-extract/vite-plugin": "^5.0.6", "@vitejs/plugin-react": "^5.0.0", "jsdom": "^26.1.0", "vite": "^7.1.2"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "main": "index.js", "keywords": [], "author": "", "license": "ISC", "description": ""}