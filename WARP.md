# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

## About Rolewise.ai

Rolewise.ai is an intelligent identity management platform that uses machine learning to derive contextual understanding of access privileges. It automatically analyzes access patterns and organizational structures to provide intelligent recommendations and insights, moving away from manual role and permission configuration.

## Architecture Overview

### Multi-Tenant Architecture
- **Frontend**: React 19 + TypeScript single-page application
- **Backend**: Supabase (PostgreSQL database + Edge Functions)
- **Authentication**: Auth0 (recently migrated from <PERSON> via Supabase Auth)
- **State Management**: Zustand stores for global state
- **Styling**: vanilla-extract CSS for type-safe styling

### Key Architectural Concepts
- **Tenant Isolation**: Each organization has its own isolated environment with row-level security
- **ML-Driven Role Discovery**: Uses k-means clustering on vector embeddings to automatically identify role patterns
- **Microsoft Entra ID Integration**: Imports users, groups, and applications from Microsoft Entra ID
- **Edge Functions Workflow**: Orchestrated data processing pipeline for role inference

### Repository Structure
```
rolewise.ai/
├── frontend/               # React 19 frontend application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Route-based page components  
│   │   ├── stores/         # Zustand state management
│   │   ├── services/       # API clients and integrations
│   │   ├── types/          # TypeScript type definitions
│   │   └── styles/         # vanilla-extract CSS styles
├── supabase/
│   ├── functions/          # Edge Functions for ML processing
│   │   ├── fetch-entra-data/        # Entra ID data ingestion
│   │   ├── infer-roles/             # ML role inference
│   │   ├── orchestrate-tenant-processing/  # Workflow orchestration
│   │   └── process-tenant-data/     # Data processing pipeline
│   └── migrations/         # Database schema migrations
├── migration/              # Auth0 migration scripts and tools
└── data/                   # Sample data and fixtures
```

## Development Commands

### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server (http://localhost:5173)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### Database & Backend
```bash
# Navigate to project root for Supabase operations
cd /path/to/rolewise.ai

# Deploy all Edge Functions
supabase functions deploy

# Deploy specific Edge Function
supabase functions deploy [function-name]

# View Edge Function logs
supabase functions logs [function-name]

# Set secrets for Edge Functions
supabase secrets set OPENAI_API_KEY=your_key
```

### Migration Tools
```bash
# Navigate to migration directory
cd migration

# Setup migration environment
./setup.sh

# Run migration dry-run (test without changes)
npm run dry-run

# Execute user migration to Auth0
npm run migrate

# Run post-migration tasks
npm run post-migration
```

## Core Application Flow

### Authentication & Tenant Management
1. User registers/logs in via Auth0
2. Auth0Integration component manages authentication state
3. On first login, Edge Function creates new tenant automatically
4. All data operations are scoped to user's tenant via row-level security

### Role Inference Pipeline
1. **Data Ingestion**: `fetch-entra-data` pulls user/group/app data from Microsoft Entra ID
2. **Processing**: `process-tenant-data` normalizes and prepares data
3. **ML Analysis**: `infer-roles` generates embeddings and performs clustering
4. **Orchestration**: `orchestrate-tenant-processing` coordinates the full pipeline

### State Management Pattern
- **authStore.ts**: Manages Auth0 authentication state and user context
- **rolewiseStore.ts**: Contains business logic for users, roles, permissions, and insights
- Components subscribe to relevant store slices and trigger actions

### API Integration Pattern
- **services/api/supabase.ts**: Database operations and Edge Function calls
- **services/api/entra-id-auth.ts**: Microsoft Entra ID integration
- **services/supabaseClient.ts**: Configured Supabase client instance

## Environment Configuration

### Frontend Environment Variables
```bash
# Required for Auth0 authentication
VITE_AUTH0_DOMAIN=your-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_AUDIENCE=your-auth0-api-identifier

# Required for Supabase integration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Edge Function Secrets
```bash
# Required for ML role inference
OPENAI_API_KEY=your_openai_api_key

# Required for Entra ID integration
ENTRA_CLIENT_ID=your_entra_client_id
ENTRA_CLIENT_SECRET=your_entra_client_secret
ENTRA_TENANT_ID=your_entra_tenant_id
```

## Testing Architecture

- **Framework**: Vitest with jsdom environment
- **Setup**: `src/test/setup.ts` configures test environment
- **Coverage**: Configured for text, JSON, and HTML reports
- **Workspace**: Separate browser and server test environments
- **Mocking**: Test environment variables override production config

## Development Guidelines

### Adding New Features
1. Create components in appropriate subdirectories under `src/components/`
2. Define TypeScript types in `src/types/`
3. Add API functions to `src/services/api/`
4. Create or extend Zustand stores for state management
5. Add styles using vanilla-extract in `.css.ts` files

### Database Schema Changes
1. Create new migration files in `supabase/migrations/`
2. Test locally with Supabase CLI
3. Deploy via `supabase db push` or through dashboard

### Edge Function Development
1. Create new function directories under `supabase/functions/`
2. Use shared utilities from `supabase/functions/_shared/`
3. Test locally with `supabase functions serve`
4. Deploy with `supabase functions deploy [function-name]`

## Critical Dependencies

- **React 19**: Latest React with modern features
- **Auth0**: Authentication provider (migrated from Clerk)
- **Supabase**: Backend-as-a-Service with PostgreSQL
- **Zustand**: Lightweight state management
- **vanilla-extract**: Type-safe CSS-in-TypeScript
- **React Flow**: For visualizing role relationships
- **Vite**: Build tool and development server
- **Vitest**: Testing framework
