{"name": "supabase-auth0-migration", "version": "1.0.0", "description": "Migration script to move users from Supabase Auth to Auth0", "main": "supabase-to-auth0-migration.js", "scripts": {"migrate": "node supabase-to-auth0-migration.js", "dry-run": "node dry-run-migration.js", "rollback": "node rollback-migration.js", "verify": "node supabase-to-auth0-migration.js --verify", "post-migration": "node post-migration-tasks.js", "send-reset-emails": "node post-migration-tasks.js --password-reset", "generate-templates": "node post-migration-tasks.js --templates", "migration-report": "node post-migration-tasks.js --report"}, "dependencies": {"@supabase/supabase-js": "^2.49.10", "auth0": "^4.11.0", "dotenv": "^17.2.1"}, "devDependencies": {"@types/node": "^24.3.0"}, "keywords": ["migration", "supabase", "auth0", "user-migration"], "author": "Rolewise.ai", "license": "MIT"}