# Supabase to Auth0 User Migration

This directory contains scripts to migrate users from Supabase Auth to Auth0 while preserving all user data and relationships in your database.

## Overview

The migration process:
1. **Exports** all users from Supabase Auth
2. **Creates** corresponding users in Auth0
3. **Updates** database references from Supabase user IDs to Auth0 user IDs
4. **Preserves** all user relationships (tenants, roles, groups, etc.)

## Prerequisites

### 1. Auth0 Setup
- Create an Auth0 account and tenant
- Create a Machine-to-Machine application with the following scopes:
  - `read:users`
  - `create:users`
  - `update:users`
- Note down your Domain, Client ID, and Client Secret

### 2. Supabase Access
- Ensure you have access to your Supabase project
- Get your Service Role Key (not the anon key)

### 3. Environment Setup
```bash
# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your actual credentials
nano .env
```

## Migration Steps

### Step 1: Dry Run (Recommended)
Always run a dry run first to see what will happen:

```bash
npm run dry-run
```

This will:
- Show all users that will be migrated
- Check which users already exist in Auth0
- Display potential issues
- Show database changes that would be made

### Step 2: Run Migration
If the dry run looks good, proceed with the actual migration:

```bash
npm run migrate
```

This will:
- Create users in Auth0 with temporary passwords
- Update all database references
- Generate a mapping file for rollback purposes

### Step 3: Verify Migration
After migration:
1. Check that users can log in to Auth0
2. Verify that all user data is preserved
3. Test your application with Auth0 authentication

## Important Notes

### User Passwords
- **Passwords cannot be migrated** from Supabase to Auth0
- Users will be created with temporary passwords
- Users will need to reset their passwords on first login
- Consider sending password reset emails to all users

### User IDs
- Supabase user IDs will be replaced with Auth0 user IDs
- The original Supabase ID is stored in Auth0 user metadata
- All database relationships are updated automatically

### Email Verification
- Email verification status is preserved
- Unverified emails in Supabase will remain unverified in Auth0

## Rollback

If something goes wrong, you can rollback the database changes:

```bash
npm run rollback
```

**Note:** This only rolls back database changes. Auth0 users are NOT deleted.

## Files Generated

- `user-migration-mapping.json` - Complete mapping of Supabase to Auth0 user IDs
- `rollback-log.json` - Log of rollback operations (if rollback is performed)

## Troubleshooting

### Common Issues

1. **Rate Limiting**
   - Auth0 has rate limits on user creation
   - The script includes delays to avoid this
   - If you hit limits, wait and re-run

2. **Duplicate Emails**
   - If users already exist in Auth0, they won't be recreated
   - The script will map existing Auth0 users to Supabase users

3. **Missing Permissions**
   - Ensure your Auth0 Machine-to-Machine app has the correct scopes
   - Check that your Supabase Service Role Key is correct

### Error Recovery

If the migration fails partway through:
1. Check the error logs
2. Fix the underlying issue
3. Re-run the migration (it will skip already-created users)

## Security Considerations

1. **Temporary Passwords**: Users are created with random temporary passwords
2. **Metadata**: Supabase user metadata is preserved in Auth0
3. **Service Keys**: Keep your service role keys secure
4. **Cleanup**: Consider cleaning up temporary files after migration

## Post-Migration Tasks

1. **Update Frontend**: Ensure your frontend is using Auth0 (already done if following the main migration guide)
2. **Update Backend**: Update your edge functions to validate Auth0 JWTs
3. **User Communication**: Notify users about the migration and password reset requirement
4. **Cleanup**: Remove old Supabase auth data (optional, keep for backup initially)

## Support

If you encounter issues:
1. Check the error logs in the console output
2. Review the generated mapping files
3. Ensure all prerequisites are met
4. Consider running the dry-run again to identify issues
