-- Auth0 Schema Migration Script
-- This script converts UUID user_id columns to TEXT to support Auth0 user IDs

-- Step 1: Backup current data (optional, for safety)
-- CREATE TABLE profiles_backup AS SELECT * FROM profiles;
-- CREATE TABLE user_roles_backup AS SELECT * FROM user_roles;
-- CREATE TABLE user_tenants_backup AS SELECT * FROM user_tenants;

-- Step 2: Disable R<PERSON> temporarily to avoid conflicts
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_tenants DISABLE ROW LEVEL SECURITY;

-- Step 3: Drop foreign key constraints that reference user IDs
-- (We'll need to recreate these later if they exist)

-- Step 4: Change profiles.id from UUID to TEXT
ALTER TABLE profiles ALTER COLUMN id TYPE TEXT USING id::TEXT;

-- Step 5: Change user_roles.user_id from UUID to TEXT
ALTER TABLE user_roles ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;

-- Step 6: Change user_tenants.user_id from UUID to TEXT
ALTER TABLE user_tenants ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;

-- Step 7: Update the specific user record with Auth0 ID
UPDATE profiles 
SET id = 'auth0|684363208d0f406b0f06f149' 
WHERE email = '<EMAIL>' 
  AND id = '600070af-353c-46e6-9a71-84789f749c02';

UPDATE user_roles 
SET user_id = 'auth0|684363208d0f406b0f06f149' 
WHERE user_id = '600070af-353c-46e6-9a71-84789f749c02';

UPDATE user_tenants 
SET user_id = 'auth0|684363208d0f406b0f06f149' 
WHERE user_id = '600070af-353c-46e6-9a71-84789f749c02';

-- Step 8: Create or update RLS policies for Auth0 compatibility
-- Drop existing policies that use auth.uid()
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can create own profile" ON profiles;
DROP POLICY IF EXISTS "Users manage own roles" ON user_roles;
DROP POLICY IF EXISTS "user_access" ON user_tenants;

-- Create new policies that work with Auth0 tokens
-- Note: These policies will need to be updated to work with Auth0 JWT validation
-- For now, we'll create permissive policies and secure them later

-- Temporary permissive policy for profiles (to be secured later)
CREATE POLICY "auth0_profiles_access" ON profiles
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Temporary permissive policy for user_roles (to be secured later)
CREATE POLICY "auth0_user_roles_access" ON user_roles
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Temporary permissive policy for user_tenants (to be secured later)
CREATE POLICY "auth0_user_tenants_access" ON user_tenants
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Step 9: Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_tenants ENABLE ROW LEVEL SECURITY;

-- Step 10: Create a function to extract user ID from Auth0 JWT
-- This will be used in future RLS policies
CREATE OR REPLACE FUNCTION auth0_user_id()
RETURNS TEXT AS $$
BEGIN
    -- Extract user ID from Auth0 JWT token
    -- This is a placeholder - will need to be implemented based on Auth0 JWT structure
    RETURN COALESCE(
        current_setting('request.jwt.claims', true)::json->>'sub',
        current_setting('app.current_user_id', true)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 11: Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth0_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth0_user_id() TO anon;
