# Auth0 Compatibility Solution

## Problem Summary
The database schema uses UUID columns for user IDs, but Auth0 user IDs are strings like `auth0|684363208d0f406b0f06f149`. Additionally, RLS policies use Supabase auth functions that don't work with Auth0 tokens.

## Immediate Solution Options

### Option 1: Quick Fix - Disable RLS Temporarily
This allows the application to work immediately while we plan a proper solution.

### Option 2: Create User Mapping Table
Create a mapping between Auth0 user IDs and internal UUID user IDs.

### Option 3: Complete Schema Migration (Complex)
Change all user_id columns to TEXT and update all policies.

## Recommended Approach: Option 1 + Option 2

### Step 1: Immediate Fix - Disable RLS
```sql
-- Disable RLS on key tables to allow immediate access
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_tenants DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_group_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE inferred_roles DISABLE ROW LEVEL SECURITY;
```

### Step 2: Create User Mapping System
```sql
-- Create a mapping table between Auth0 and internal user IDs
CREATE TABLE auth0_user_mapping (
    auth0_user_id TEXT PRIMARY KEY,
    internal_user_id UUID NOT NULL,
    email TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert mapping for current user
INSERT INTO auth0_user_mapping (auth0_user_id, internal_user_id, email)
VALUES ('auth0|684363208d0f406b0f06f149', '600070af-353c-46e6-9a71-84789f749c02', '<EMAIL>');
```

### Step 3: Update Frontend to Use Mapping
The frontend will:
1. Get Auth0 user ID from Auth0
2. Look up internal UUID from mapping table
3. Use internal UUID for all database queries

### Step 4: Create Helper Functions
```sql
-- Function to get internal user ID from Auth0 user ID
CREATE OR REPLACE FUNCTION get_internal_user_id(auth0_id TEXT)
RETURNS UUID AS $$
DECLARE
    internal_id UUID;
BEGIN
    SELECT internal_user_id INTO internal_id
    FROM auth0_user_mapping
    WHERE auth0_user_id = auth0_id;
    
    RETURN internal_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get Auth0 user ID from internal user ID
CREATE OR REPLACE FUNCTION get_auth0_user_id(internal_id UUID)
RETURNS TEXT AS $$
DECLARE
    auth0_id TEXT;
BEGIN
    SELECT auth0_user_id INTO auth0_id
    FROM auth0_user_mapping
    WHERE internal_user_id = internal_id;
    
    RETURN auth0_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Implementation Steps

### Database Changes
1. Disable RLS on key tables
2. Create auth0_user_mapping table
3. Insert current user mapping
4. Create helper functions

### Frontend Changes
1. Update authStore to use mapping
2. Modify API calls to use internal user IDs
3. Update user profile initialization

### Edge Function Changes
1. Update JWT validation for Auth0
2. Use mapping system for user identification

## Security Considerations

With RLS disabled, we need to implement application-level security:
1. Validate Auth0 tokens in edge functions
2. Use tenant isolation in application logic
3. Re-enable RLS with Auth0-compatible policies later

## Migration Path

This solution provides:
1. ✅ Immediate functionality
2. ✅ Backward compatibility
3. ✅ Path to full Auth0 integration
4. ✅ Minimal breaking changes

## Next Steps

1. Execute database changes
2. Update frontend authStore
3. Test authentication flow
4. Plan full RLS migration for later
