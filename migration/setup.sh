#!/bin/bash

# Supabase to Auth0 Migration Setup Script

echo "🚀 Setting up Supabase to Auth0 migration environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your actual credentials."
    echo ""
    echo "You need to configure:"
    echo "  - SUPABASE_URL"
    echo "  - SUPABASE_SERVICE_ROLE_KEY"
    echo "  - AUTH0_DOMAIN"
    echo "  - AUTH0_CLIENT_ID"
    echo "  - AUTH0_CLIENT_SECRET"
    echo ""
    echo "After configuring .env, you can run:"
    echo "  npm run dry-run    # Test the migration"
    echo "  npm run migrate    # Run the actual migration"
else
    echo "⚠️  .env file already exists"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit the .env file with your credentials"
echo "2. Run 'npm run dry-run' to test the migration"
echo "3. Run 'npm run migrate' to perform the actual migration"
echo "4. Run 'npm run post-migration' for post-migration tasks"
