# Auth0 Setup Instructions

## The Problem
Your Auth0 Machine-to-Machine application doesn't have permission to access the Management API. This is why you're getting the "Client is not authorized" error.

## Solution: Grant Management API Access

### Option 1: Using Auth0 Dashboard (Recommended)

1. **Go to your Auth0 Dashboard**: https://manage.auth0.com/
2. **Navigate to Applications**
3. **Find your Machine-to-Machine application** (or create a new one)
4. **Click on the application name**
5. **Go to the "APIs" tab**
6. **Find "Auth0 Management API"** in the list
7. **Toggle it ON** (authorize this application)
8. **Select the following scopes**:
   - ✅ `read:users`
   - ✅ `create:users`
   - ✅ `update:users`
   - ✅ `create:user_tickets`
   - ✅ `read:user_idp_tokens` (optional)
9. **Click "Update"**

### Option 2: Create New Machine-to-Machine Application

If you don't have a Machine-to-Machine app:

1. **Applications > Create Application**
2. **Name**: "User Migration Script"
3. **Type**: "Machine to Machine Applications"
4. **Click "Create"**
5. **Select "Auth0 Management API"**
6. **Select the scopes listed above**
7. **Click "Authorize"**
8. **Copy the Client ID and Client Secret**
9. **Update your `.env` file**:
   ```
   AUTH0_CLIENT_ID=your-new-client-id
   AUTH0_CLIENT_SECRET=your-new-client-secret
   ```

## Verify Setup

After setting up the permissions, test the connection:

```bash
node test-env.js
```

This should show that the Auth0 client is created successfully.

## Common Issues

### Issue: "Client is not authorized"
- **Solution**: Follow the steps above to grant Management API access

### Issue: "Insufficient scope"
- **Solution**: Make sure you've selected all the required scopes listed above

### Issue: "Invalid client"
- **Solution**: Double-check your Client ID and Client Secret in the `.env` file

## Next Steps

Once you've set up the permissions:

1. **Test the connection**: `node test-env.js`
2. **Run dry run**: `npm run dry-run`
3. **Run migration**: `npm run migrate`

## Security Note

The Machine-to-Machine application credentials have powerful permissions. Keep them secure and consider deleting the application after the migration is complete.
