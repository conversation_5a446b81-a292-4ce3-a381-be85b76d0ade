import { createClient } from 'jsr:@supabase/supabase-js@2.49.4';

interface Auth0User {
  sub: string;
  email?: string;
  name?: string;
  internalUserId?: string;
}

interface Auth0AuthResult {
  success: boolean;
  user?: Auth0User;
  error?: string;
}

/**
 * Validates Auth0 JWT token and returns user information
 * This function handles the conversion from Auth0 tokens to internal user IDs
 */
export async function validateAuth0Token(
  authHeader: string | null,
  supabaseUrl: string,
  serviceRoleKey: string
): Promise<Auth0AuthResult> {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      success: false,
      error: 'Missing or invalid Authorization header'
    };
  }

  const token = authHeader.replace('Bearer ', '');
  
  try {
    // Create admin client to access the mapping table
    const adminClient = createClient(supabaseUrl, serviceRoleKey);
    
    // For now, we'll use a simple approach - extract user info from token
    // In production, you should validate the Auth0 JWT properly
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      return {
        success: false,
        error: 'Invalid JWT token format'
      };
    }

    // Decode the payload (this is a simplified approach)
    const payload = JSON.parse(atob(tokenParts[1]));
    const auth0UserId = payload.sub;
    const email = payload.email;
    const name = payload.name;

    if (!auth0UserId) {
      return {
        success: false,
        error: 'No user ID found in token'
      };
    }

    // Look up internal user ID from mapping table
    const { data: mapping, error: mappingError } = await adminClient
      .from('auth0_user_mapping')
      .select('internal_user_id')
      .eq('auth0_user_id', auth0UserId)
      .single();

    if (mappingError || !mapping) {
      return {
        success: false,
        error: 'User mapping not found. Please contact your administrator.'
      };
    }

    return {
      success: true,
      user: {
        sub: auth0UserId,
        email,
        name,
        internalUserId: mapping.internal_user_id
      }
    };

  } catch (error) {
    console.error('Auth0 token validation error:', error);
    return {
      success: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Creates a Supabase client with Auth0 authentication
 * This client will use the internal user ID for database operations
 */
export function createAuth0SupabaseClient(
  supabaseUrl: string,
  serviceRoleKey: string,
  internalUserId: string
) {
  // Create a client with service role key but with user context
  const client = createClient(supabaseUrl, serviceRoleKey, {
    global: {
      headers: {
        'X-User-ID': internalUserId,
        'X-Auth-Provider': 'auth0'
      }
    }
  });

  return client;
}

/**
 * Helper function to check if user has access to a specific tenant
 */
export async function checkTenantAccess(
  supabaseClient: ReturnType<typeof createClient>,
  userId: string,
  tenantId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabaseClient
      .from('user_tenants')
      .select('*')
      .eq('user_id', userId)
      .eq('tenant_id', tenantId)
      .maybeSingle();

    if (error) {
      console.error('Tenant access check error:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Tenant access check failed:', error);
    return false;
  }
}
