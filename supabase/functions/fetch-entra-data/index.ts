import { createClient } from 'jsr:@supabase/supabase-js@2.49.4';
import { corsHeaders } from '../_shared/cors.ts';
import { CacheManager } from '../_shared/cache.ts';
import { validateAuth0Token, createAuth0SupabaseClient, checkTenantAccess } from '../_shared/auth0-auth.ts';

interface FetchEntraDataPayload {
  tenantId: string;
  forceRefresh?: boolean;
}

Deno.serve(async (req) => {
  console.log(`Request: ${req.method} ${req.url}`, {
    headers: Object.fromEntries(req.headers),
  });

  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const authHeader = req.headers.get('Authorization');
    console.log('Authorization header:', authHeader || 'Missing');
    
    // Validate Auth0 token and get user information
    const authResult = await validateAuth0Token(authHeader, supabaseUrl, serviceRoleKey);
    if (!authResult.success || !authResult.user) {
      return new Response(
        JSON.stringify({ error: 'Authentication failed', details: authResult.error }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const { tenantId, forceRefresh }: FetchEntraDataPayload = await req.json();
    if (!tenantId) {
      return new Response(
        JSON.stringify({ error: 'Missing tenantId' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    console.log(`Request parameters: tenantId=${tenantId}, forceRefresh=${!!forceRefresh}`);

    // Create Supabase client with Auth0 authentication
    const supabaseClient = createAuth0SupabaseClient(supabaseUrl, serviceRoleKey, authResult.user.internalUserId!);
    
    // Check tenant access
    const hasAccess = await checkTenantAccess(supabaseClient, authResult.user.internalUserId!, tenantId);
    if (!hasAccess) {
      return new Response(
        JSON.stringify({ error: 'Not authorized to access this tenant' }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const adminClient = createClient(supabaseUrl, serviceRoleKey);
    const { data: userTenants, error: tenantError } = await adminClient
      .from('user_tenants')
      .select('*')
      .eq('user_id', user.id)
      .eq('tenant_id', tenantId)
      .maybeSingle();

    console.log('user_tenants query:', {
      user_id: user.id,
      tenant_id: tenantId,
      userTenants,
      tenantError: tenantError ? tenantError.message : null,
    });

    if (tenantError) {
      console.log('Tenant query error:', tenantError);
      if (tenantError.code === '42P01') {
        return new Response(
          JSON.stringify({ error: 'User tenants table does not exist. Please contact your administrator.' }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }
      return new Response(
        JSON.stringify({ error: 'Failed to verify tenant access', details: tenantError.message }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    if (!userTenants) {
      console.log('No tenant access found for user:', { user_id: user.id, tenant_id: tenantId });
      return new Response(
        JSON.stringify({ error: 'Not authorized to access this tenant' }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const { data: integration, error } = await adminClient
      .from('integrations')
      .select('client_id, app_tenant_id, client_secret')
      .eq('tenant_id', tenantId)
      .maybeSingle();

    console.log('Integration query results:', {
      hasIntegration: !!integration,
      error: error ? error.message : null
    });

    if (error) {
      if (error.code === '42P01') {
        return new Response(
          JSON.stringify({ error: 'Integrations table does not exist. Please contact your administrator.' }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }
      return new Response(
        JSON.stringify({ error: 'No integration found for this tenant. Please set up Microsoft Entra ID integration first.' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    if (!integration) {
      return new Response(
        JSON.stringify({ error: 'No integration found for this tenant. Please set up Microsoft Entra ID integration first.' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const { client_id, app_tenant_id, client_secret } = integration;
    console.log('Integration credentials found:', {
      hasClientId: !!client_id,
      hasAppTenantId: !!app_tenant_id,
      hasClientSecret: !!client_secret
    });

    const tokenRequestBody = {
      tenant_id: app_tenant_id,
      client_id,
      client_secret,
      force_refresh: forceRefresh, // Pass through the force refresh flag
    };

    console.log(`Requesting token from ${supabaseUrl}/functions/v1/get-entra-token with payload:`, {
      tenant_id_exists: !!tokenRequestBody.tenant_id,
      client_id_exists: !!tokenRequestBody.client_id,
      client_secret_exists: !!tokenRequestBody.client_secret,
      force_refresh: !!tokenRequestBody.force_refresh,
    });

    const tokenResponse = await fetch(`${supabaseUrl}/functions/v1/get-entra-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': req.headers.get('Authorization')!,
      },
      body: JSON.stringify(tokenRequestBody),
    });

    console.log('Token response status:', tokenResponse.status);

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token response error:', errorText);

      try {
        const errorData = JSON.parse(errorText);
        return new Response(
          JSON.stringify({
            error: 'Failed to obtain access token',
            details: errorData.error || errorData.message || errorText
          }),
          {
            status: tokenResponse.status,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      } catch (e) {
        return new Response(
          JSON.stringify({
            error: 'Failed to obtain access token',
            details: `Status ${tokenResponse.status}: ${errorText}`
          }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;
    console.log('Access token obtained:', !!accessToken);

    if (!accessToken) {
      return new Response(
        JSON.stringify({ error: 'No access token returned from server', details: tokenData }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Initialize cache manager
    const cacheManager = new CacheManager(supabaseUrl, serviceRoleKey);

    // Check if we have cached data and should use it
    if (!forceRefresh) {
      console.log('Checking for cached data');

      // Try to get cached data for all three data types
      const cachedApps = await cacheManager.getData(tenantId, 'applications');
      const cachedGroups = await cacheManager.getData(tenantId, 'groups');
      const cachedUsers = await cacheManager.getData(tenantId, 'users');

      // If we have all three types of data cached, return them
      if (cachedApps && cachedGroups && cachedUsers) {
        console.log('Using cached data for all three data types');
        return new Response(
          JSON.stringify({
            applications: cachedApps,
            groups: cachedGroups,
            users: cachedUsers,
            cached: true
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }

      console.log('Cache miss for at least one data type, fetching fresh data');
    } else {
      console.log('Force refresh requested, skipping cache');
    }

    console.log('Making requests to Microsoft Graph API with the token');

    try {
      const appsResponse = await fetch('https://graph.microsoft.com/v1.0/applications', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      const appsData = await appsResponse.json();
      console.log('Applications API response status:', appsResponse.status);

      if (!appsResponse.ok) {
        console.error('Applications API error:', appsData);
        return new Response(
          JSON.stringify({ error: 'Failed to fetch applications data', details: appsData }),
          {
            status: appsResponse.status,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }

      const groupsResponse = await fetch('https://graph.microsoft.com/v1.0/groups', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      const groupsData = await groupsResponse.json();
      console.log('Groups API response status:', groupsResponse.status);

      if (!groupsResponse.ok) {
        console.error('Groups API error:', groupsData);
        return new Response(
          JSON.stringify({ error: 'Failed to fetch groups data', details: groupsData }),
          {
            status: groupsResponse.status,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }

      const usersResponse = await fetch('https://graph.microsoft.com/v1.0/users?$select=id&$expand=memberOf', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      const usersData = await usersResponse.json();
      console.log('Users API response status:', usersResponse.status);

      if (!usersResponse.ok) {
        console.error('Users API error:', usersData);
        return new Response(
          JSON.stringify({ error: 'Failed to fetch users data', details: usersData }),
          {
            status: usersResponse.status,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }

      // Process the data
      const applications = appsData.value?.map((app: any) => ({
        id: app.id,
        displayName: app.displayName,
        description: app.description || null,
      })) || [];

      const groups = groupsData.value?.map((group: any) => ({
        groupId: group.id,
        displayName: group.displayName,
        description: group.description,
      })) || [];

      const users = usersData.value?.map((user: any) => ({
        userId: user.id,
        groupIds: (user.memberOf || []).map((g: any) => g.id),
      })) || [];

      // Cache the processed data
      try {
        await Promise.all([
          cacheManager.saveData(tenantId, 'applications', applications),
          cacheManager.saveData(tenantId, 'groups', groups),
          cacheManager.saveData(tenantId, 'users', users)
        ]);
        console.log('Successfully cached all data');
      } catch (cacheError) {
        // Log but don't fail if caching fails
        console.error('Error caching data:', cacheError);
      }

      return new Response(
        JSON.stringify({
          applications,
          groups,
          users,
          cached: false
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    } catch (graphError) {
      console.error('Graph API request error:', graphError);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch Microsoft Graph data', details: graphError instanceof Error ? graphError.message : String(graphError) }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }
  } catch (error: unknown) {
    console.error('Error in fetch-entra-data:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new Response(
      JSON.stringify({ error: errorMessage || 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});