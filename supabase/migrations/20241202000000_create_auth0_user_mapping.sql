-- Create Auth0 user mapping table
CREATE TABLE IF NOT EXISTS auth0_user_mapping (
    auth0_user_id TEXT PRIMARY KEY,
    internal_user_id UUID NOT NULL,
    email TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_auth0_user_mapping_internal_id ON auth0_user_mapping(internal_user_id);
CREATE INDEX IF NOT EXISTS idx_auth0_user_mapping_email ON auth0_user_mapping(email);

-- Add RLS policies for the mapping table
ALTER TABLE auth0_user_mapping ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own mapping
CREATE POLICY "Users can view own auth0 mapping" ON auth0_user_mapping
    FOR SELECT
    TO authenticated
    USING (auth0_user_id = auth0_user_id());

-- Allow service role full access
CREATE POLICY "Service role auth0 mapping access" ON auth0_user_mapping
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- <PERSON><PERSON> helper functions for Auth0 integration
CREATE OR REPLACE FUNCTION auth0_user_id()
RETURNS TEXT AS $$
BEGIN
    -- Extract user ID from Auth0 JWT token
    RETURN COALESCE(
        current_setting('request.jwt.claims', true)::json->>'sub',
        current_setting('app.current_user_id', true)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get internal user ID from Auth0 user ID
CREATE OR REPLACE FUNCTION get_internal_user_id(auth0_id TEXT)
RETURNS UUID AS $$
DECLARE
    internal_id UUID;
BEGIN
    SELECT internal_user_id INTO internal_id
    FROM auth0_user_mapping
    WHERE auth0_user_id = auth0_id;
    
    RETURN internal_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get Auth0 user ID from internal user ID
CREATE OR REPLACE FUNCTION get_auth0_user_id(internal_id UUID)
RETURNS TEXT AS $$
DECLARE
    auth0_id TEXT;
BEGIN
    SELECT auth0_user_id INTO auth0_id
    FROM auth0_user_mapping
    WHERE internal_user_id = internal_id;
    
    RETURN auth0_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION auth0_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth0_user_id() TO anon;
GRANT EXECUTE ON FUNCTION get_internal_user_id(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_auth0_user_id(UUID) TO authenticated;
